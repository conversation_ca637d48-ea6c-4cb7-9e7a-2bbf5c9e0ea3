# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/) and this project loosely adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html), however we do not have a public API to measure against.

These releases are tagged in GIT.

Given a version number MAJOR.MINOR.PATCH, increment the:

**MAJOR** version when you make changes incompatible with the previous version. Examples are:

- existing data models (and their data) are made redundant
- large interface change across all sections
- rollout of an entirely new top-level section or area that affects existing sections or areas

**MINOR** version when you add functionality in a manner compatible with the previous version. You would expect these to be on a `feature` branch in GIT. Examples are:

- upgrading or updating existing multiple 3rd party libraries and gems
- modification that touches the model, view and controller
- adding functionality to an existing section
- making layout changes or improvements across multiple sections

**PATCH** version when you make changes in a manner compatible with the previous version. You would expect these to be single commit or small amount of commits made directly on the `main` branch. Examples are:

- bug fixes
- smaller layout fixes or adjustments
- upgrading or updating a single 3rd party library or gem
- altering existing functionality slightly

All version changes should be grouped under **New**, **Change** or **Fix**.
The changes below will be used as template when communicating updates throughout i-Motor.

---

## NOT YET RELEASED

### New
- TBA. Link to any Pull Requests, Issues, tickets etc. Please tag with your GitHub username. `@GitHubUsername`
- Core. Dealership Model. [PR#6](https://github.com/imotor-code/dealer-drive-backend/pull/6) by @enliven-
- Core. Github Actions. [PR#9](https://github.com/imotor-code/dealer-drive-backend/pull/9) @pankajbatra
- Core. Added and updated driver-related models and validations (Customer, DriverLicense, DriverSignature, DamageReport) with comprehensive test coverage. `@saurabhlodha`
- Core. Model code for Dealership, DealershipGroup, and other entities and necessary specs [PR#25](https://github.com/imotor-code/dealer-drive-backend/pull/25) by @enliven-
- Core. Added GpsLocation model with polymorphic associations for Vehicle's last known location and Drive's waypoints tracking [PR#30](https://github.com/imotor-code/dealer-drive-backend/pull/30) by @saurabhlodha
- Core. Added ActiveStorage attachments and UUID support to Vehicle, Customer, Drive, DamageReport and DriverLicense models [PR#31](https://github.com/imotor-code/dealer-drive-backend/pull/31) by @saurabhlodha
- Core. Added booking creation endpoint with nested customer and driver license support. [PR#14](https://github.com/imotor-code/dealer-drive-backend/pull/14) by @saurabhlodha
- Core. Adds User Device Registration models and rewrites token generation and storage to include user device. Updates user seeds. [PR#33](https://github.com/imotor-code/dealer-drive-backend/pull/33) by @pankajbatra
- Core. Adds Config for Heroku Staging setup. [PR#37](https://github.com/imotor-code/dealer-drive-backend/pull/37) by @pankajbatra
- Core. Adds User-Dealership many-to-many relationship model, with different roles. Adds user types - Super Admin, Staff and Dealership user. [PR#38](https://github.com/imotor-code/dealer-drive-backend/pull/38) by @pankajbatra
- Core. Adds Dealership controller/actions and related request specs; Also adds missing website field on Dealership and a validator. [PR#36](https://github.com/imotor-code/dealer-drive-backend/pull/36) by @pankajbatra
- Core. Adds functionality for sending emails using sendgrid [PR#34](https://github.com/imotor-code/dealer-drive-backend/pull/34) by @cldyd9
- Core. Adds functionality to add/delete photo through /api/v1/profile/photo api calls. [PR#39](https://github.com/imotor-code/dealer-drive-backend/pull/39) by @cldyd9
- Core. Adds functionality to forgot/change password APIs. [PR#41](https://github.com/imotor-code/dealer-drive-backend/pull/41) by @cldyd9
- Core. Adds functionality to reset password/verify reset code APIs. [PR#46](https://github.com/imotor-code/dealer-drive-backend/pull/46) by @cldyd9
- Core. Adds APIs to update user profile and update the device information. [PR#51](https://github.com/imotor-code/dealer-drive-backend/pull/51) by @pankajbatra
- Core. Adds APIs to list/create/update/view dealership's customers. [PR#62](https://github.com/imotor-code/dealer-drive-backend/pull/62) by @prateekpj97
- Core. Add functionality to limit number of resends for otp and password reset code. [PR#61](https://github.com/imotor-code/dealer-drive-backend/pull/61) by @cldyd9
- Core. Adds TOTP (Time-based One-Time Password) authentication functionality for enhanced user security. [PR#53](https://github.com/imotor-code/dealer-drive-backend/pull/53) by @anmoldhingra11
- Core. Adds functionality to user driving license APIs.   [PR#52](https://github.com/imotor-code/dealer-drive-backend/pull/52) by @prateekpj97
- Core. Adds API to fetch dealership users. [PR#70](https://github.com/imotor-code/dealer-drive-backend/pull/70) by @prateekpj97
- Core. Adds SMS functionality using Twilio for OTP delivery, password reset, and password change verification codes. [PR#69](https://github.com/imotor-code/dealer-drive-backend/pull/69) by @anmoldhingra11
- Core. Adds Trade Plate models, APIs, seed data and specs [PR#67](https://github.com/imotor-code/dealer-drive-backend/pull/67) by @pankajbatra
- Core. Adds APIs to list/update/cancel bookings. [PR#74](https://github.com/imotor-code/dealer-drive-backend/pull/74)
- Core. Adds API to create vehicles for dealership. [PR#76](https://github.com/imotor-code/dealer-drive-backend/pull/76) by @prateekpj97
- Core. Adds role-based user invitation system with temporary password generation and email notifications. [PR#75](https://github.com/imotor-code/dealer-drive-backend/pull/75) by @anmoldhingra11
- Core. Adds APIs to fetch dealership drives and show dealership drive. [PR#82](https://github.com/imotor-code/dealer-drive-backend/pull/82) by @prateekpj97
- Core. Adds Update return-time and odometer reading APIs. [PR#84](https://github.com/imotor-code/dealer-drive-backend/pull/84) by @anmoldhingra11
- Core. Adds APIs to reassign drive to different user. [PR#87](https://github.com/imotor-code/dealer-drive-backend/pull/87) by @prateekpj97
- Core. Adds APIs to initialise test drive model. [PR#89](https://github.com/imotor-code/dealer-drive-backend/pull/89) by @amitwork9019
- Core. Add CustomerVehicle related models. [PR#97](https://github.com/imotor-code/dealer-drive-backend/pull/97) by @anmoldhingra11
- Core. Add APIs for Drive Damage Report. [PR#92](https://github.com/imotor-code/dealer-drive-backend/pull/92) by @prateekpj97
- Core. Add FinanceDetails model. [PR#100](https://github.com/imotor-code/dealer-drive-backend/pull/100) by @anmoldhingra11
- Core. Adds an API to assign a trade plate to a Drive. [PR#102](https://github.com/imotor-code/dealer-drive-backend/pull/102) by @amitwork9019
- Core. Adds an API to create enquiry. [PR#109](https://github.com/imotor-code/dealer-drive-backend/pull/109) by @prateekpj97
- Core. Adds an API to update drive with existing customer. [PR#101](https://github.com/imotor-code/dealer-drive-backend/pull/101) by @prateekpj97
- Core. Add OptionsFitted Model. [PR#104](https://github.com/imotor-code/dealer-drive-backend/pull/104) by @anmoldhingra11
- Core. Add VehicleHistory model. [PR#111](https://github.com/imotor-code/dealer-drive-backend/pull/111) by @anmoldhingra11 
- Core. Add Vehicle Conditions model. [PR#105](https://github.com/imotor-code/dealer-drive-backend/pull/105) by @anmoldhingra11
- Core. Adds Brand model usage in APIs and Responses. [PR#110](https://github.com/imotor-code/dealer-drive-backend/pull/110) by @amitwork9019
- Core. Add Appraisals model. [PR#122](https://github.com/imotor-code/dealer-drive-backend/pull/122) by @anmoldhingra11
- Core. Adds an API to return stats for mobile app homescreen. [PR#121](https://github.com/imotor-code/dealer-drive-backend/pull/121) by @pankajbatra
- Core. Adds an API to update drive sold status. [PR#127](https://github.com/imotor-code/dealer-drive-backend/pull/127) by @prateekpj97
- Core. Add Update Location API to record GPS Location Data. [PR#132](https://github.com/imotor-code/dealer-drive-backend/pull/132) by @anmoldhingra11
- Core. Adds an API to Attach Customer Signature in a Drive. [PR#130](https://github.com/imotor-code/dealer-drive-backend/pull/130) by @prateekpj97
- Core. Adds an API to get agreement texts for a dealership. [PR#133](https://github.com/imotor-code/dealer-drive-backend/pull/133) by @amitwork9019
- Core. Adds an API to initialise appraisals. [PR#141](https://github.com/imotor-code/dealer-drive-backend/pull/141) by @anmoldhingra11
- Core. Adds an API to create test drive/loan from booking. [PR#137](https://github.com/imotor-code/dealer-drive-backend/pull/137) by @prateekpj97
- Core. Add Appraisal seed data. [PR#179](https://github.com/imotor-code/dealer-drive-backend/pull/179) by @prateekpj97
- Core. Adds an API to delete appraisal. [PR#170](https://github.com/imotor-code/dealer-drive-backend/pull/170) by @prateekpj97
- Core. Adds boolean for fuel level in DealershipFeaturesSetting for test drives and loans. [PR#181](https://github.com/imotor-code/dealer-drive-backend/pull/181) fixes [Issue#175](https://github.com/imotor-code/dealer-drive-backend/issues/175) by @amitwork9019
- Core. Adds an API to show appraisal. [PR#161](https://github.com/imotor-code/dealer-drive-backend/pull/161) by @prateekpj97
- Core. Adds an API to create and Update Customer Vehicle for appraisal. [PR#144](https://github.com/imotor-code/dealer-drive-backend/pull/144) by @anmoldhingra11
- Core. Adds an API to create and Update Finance Details for appraisal. [PR#166](https://github.com/imotor-code/dealer-drive-backend/pull/166) by @anmoldhingra11
- Core. Adds an API to get all appraisals. [PR#159](https://github.com/imotor-code/dealer-drive-backend/pull/159) by @prateekpj97
- Core. Adds Vehicle Condition, Body Part Condition, Component Rating, Reconditioning Cost APIs. [PR#180](https://github.com/imotor-code/dealer-drive-backend/pull/180) by @anmoldhingra11
- Core. Adds an API to Attach Customer Signature in a Appraisal. [PR#188](https://github.com/imotor-code/dealer-drive-backend/pull/188) by @prateekpj97
- Core. Adds an API to archive appraisal. [PR#198](https://github.com/imotor-code/dealer-drive-backend/pull/198) by @prateekpj97
- Core. Adds an API to reassign appraisal to different user. [PR#192](https://github.com/imotor-code/dealer-drive-backend/pull/192) by @prateekpj97
- Core. Adds an API to favourite an appraisal. [PR#190](https://github.com/imotor-code/dealer-drive-backend/pull/190) fixes [Issue#187](https://github.com/imotor-code/dealer-drive-backend/issues/187) by @amitwork9019
- Core. Adds an API to edit/update appraisal. [PR#191](https://github.com/imotor-code/dealer-drive-backend/pull/191) by @prateekpj97
- Core. Adds basic user management POC for new UI dashboard. [PR#140](https://github.com/imotor-code/dealer-drive-backend/pull/140) by @pankajbatra
- Core. Adds AppraisalValuer Model. [PR#200](https://github.com/imotor-code/dealer-drive-backend/pull/200) by @prateekpj97
- Core. Adds AppraisalOffer Model. [PR#204](https://github.com/imotor-code/dealer-drive-backend/pull/204) by @prateekpj97
- Core. Add Vehicle History Upsert API. [PR#203](https://github.com/imotor-code/dealer-drive-backend/pull/203) by @anmoldhinrga11
- Core. Add Options Fitted Upsert api and specs. [PR#199](https://github.com/imotor-code/dealer-drive-backend/pull/199) by @anmoldhingra11
- Core. Add Appraisal Dashboard api [PR#209](https://github.com/imotor-code/dealer-drive-backend/pull/209) by @anmoldhingra11
- Core. Add missing part_name enum values (roof, rear_fender_skirt, rear_bumper) to BodyPartCondition model with comprehensive test coverage and API spec updates [PR#230](https://github.com/imotor-code/dealer-drive-backend/pull/230) by @prateekpj97
- Core. Add notes field to options fitted. [PR#229](https://github.com/imotor-code/dealer-drive-backend/pull/229) by @prateekpj97
- Core. Add Feature to Delete Photos [PR#251](https://github.com/imotor-code/dealer-drive-backend/pull/251) by @anmoldhingra11
- Core. Add API for Delete Drive. [PR#257](https://github.com/imotor-code/dealer-drive-backend/pull/257) by @prateekpj97
- Core. Add send for offers api. [PR#250](https://github.com/imotor-code/dealer-drive-backend/pull/250) by @amitwork9019
- Core. Add customer update functionality within appraisal [PR#246](https://github.com/imotor-code/dealer-drive-backend/pull/246) by @prateekpj97
- Core. Adds API to fetch offers for appraisal. [PR#258](https://github.com/imotor-code/dealer-drive-backend/pull/258) by @amitwork9019
- Core. Adds API to get appraisal valuers. [PR#259](https://github.com/imotor-code/dealer-drive-backend/pull/259) by @prateekpj97
- Core. Add API to award appraisal offer. [PR#262](https://github.com/imotor-code/dealer-drive-backend/pull/262) fixes [Issue#255](https://github.com/imotor-code/dealer-drive-backend/issues/255) by @amitwork9019

### Change

- TBA. Link to any Pull Requests, Issues, tickets etc. Please tag with your GitHub username. `@GitHubUsername`
- Core. Setup for Sidekiq, admin and session management. [PR#3](https://github.com/imotor-code/dealer-drive-backend/pull/3) by @cldyd9
- Core. Basic Rspec setup. [PR#4](https://github.com/imotor-code/dealer-drive-backend/pull/4) by @enliven-
- Core. Cleanup migrations. [PR#16](https://github.com/imotor-code/dealer-drive-backend/pull/16) @pankajbatra
- Core. Adds JWT access and refresh token support. [PR#20](https://github.com/imotor-code/dealer-drive-backend/pull/20) by @cldyd9
- Core. Updated token-based logic, added logic for OTP generation, validation logic and user seeds [PR#22](https://github.com/imotor-code/dealer-drive-backend/pull/22) by @cldyd9
- Core. Made email validation, has_phone_number and has_uuid concern more robust; added comprehensive specs [PR#32](https://github.com/imotor-code/dealer-drive-backend/pull/32) by @enliven-
- Core. Adds field in User model to signify whether User password change is required. [PR#40](https://github.com/imotor-code/dealer-drive-backend/pull/40) by @pankajbatra
- Core. Adds specs for user and sessions [PR#64](https://github.com/imotor-code/dealer-drive-backend/pull/64) by @cldyd9
- Core. Changes Json Serializer to blueprinter [PR#85](https://github.com/imotor-code/dealer-drive-backend/pull/85) by @pankajbatra
- Core. Add a VehicleBase Concern and Renamed Vehicle to DealershipVehicle [PR#94](https://github.com/imotor-code/dealer-drive-backend/pull/94) by @anmoldhingra11
- Core. Make changes to support Recent Activities Feed [PR#98](https://github.com/imotor-code/dealer-drive-backend/pull/98) by @pankajbatra
- Core. Adds another filter in Get Drives API to return only those drives which are eligible for marking returned [PR#119](https://github.com/imotor-code/dealer-drive-backend/pull/119) by @amitwork9019
- Core. Adds search params to GET vehicle api [PR#123](https://github.com/imotor-code/dealer-drive-backend/pull/123) by @prateekpj97
- Core. Implements an API to mark the the Test Drive or Loan completed. [PR#126](https://github.com/imotor-code/dealer-drive-backend/pull/126) by @amitwork9019
- Core. Adds an API to mark Test Drive or Loan as started/in_progress. [PR#128](https://github.com/imotor-code/dealer-drive-backend/pull/128) by @amitwork9019
- Core. Adds query param to get the drives API for free text search. [PR#129](https://github.com/imotor-code/dealer-drive-backend/pull/129) by @pankajbatra
- Core. Modifies Drive APIs to make it usable for all drive types (e.g. booking, enquiries) [PR#138](https://github.com/imotor-code/dealer-drive-backend/pull/138) by @pankajbatra
- Core. UserSerializer optimization. [PR#153](https://github.com/imotor-code/dealer-drive-backend/pull/153) by @prateekpj97
- Core. Adds Fuel Gauge Level (start and end in drive model) and exposes same on odometer API. [PR#165](https://github.com/imotor-code/dealer-drive-backend/pull/165) by @amitwork9019
- Core. Marks booking status as scheduled when expected_pickup_datetime and expected_return_datetime are set. [PR#178](https://github.com/imotor-code/dealer-drive-backend/pull/178) fixes [Issue#171](https://github.com/imotor-code/dealer-drive-backend/issues/171) by @prateekpj97
- Core. Adds more fields to user admin in dashboard. [PR#208](https://github.com/imotor-code/dealer-drive-backend/pull/208) fixes [Issue#197](https://github.com/imotor-code/dealer-drive-backend/issues/197) by @amitwork9019
- Core. Update is_finance from boolean to enum. [PR#210](https://github.com/imotor-code/dealer-drive-backend/pull/210) by @anmoldhingra11
- Core. Update is_finance default value [PR#217](https://github.com/imotor-code/dealer-drive-backend/pull/217) by @anmoldhingra11
- Core. API changes for setting and clearing given price. [PR#225](https://github.com/imotor-code/dealer-drive-backend/pull/225) fixes [Issue#223](https://github.com/imotor-code/dealer-drive-backend/issues/223) by @amitwork9019
- Core. Add video media support in models. [PR#261](https://github.com/imotor-code/dealer-drive-backend/pull/261) fixes [Issue#206](https://github.com/imotor-code/dealer-drive-backend/issues/206) and [Issue#253](https://github.com/imotor-code/dealer-drive-backend/issues/253) by @pankajbatra

### Fix

- TBA. Link to any Pull Requests, Issues, Basecamp links, tickets etc. Please tag with your GitHub username. `@GitHubUsername`
- Core. Hot reloading fix. [PR#1](https://github.com/imotor-code/dealer-drive-backend/pull/1) by @vipulbhj
- Core. Fixes local setup to run without docker. [PR#2](https://github.com/imotor-code/dealer-drive-backend/pull/2) by @saurabhlodha
- Core. Clean up Specs. Removes redundant specs. Adds missing ones to increase coverage. [PR#95](https://github.com/imotor-code/dealer-drive-backend/pull/95) by @amitwork9019
- Core. Ignores customer checks for Self loan start. [PR#168](https://github.com/imotor-code/dealer-drive-backend/pull/168) fixes [Issue#163](https://github.com/imotor-code/dealer-drive-backend/issues/163) by @amitwork9019
- Core. Add Terms and Agreement texts in Seed data. [PR#182](https://github.com/imotor-code/dealer-drive-backend/pull/182) fixes [Issue#172](https://github.com/imotor-code/dealer-drive-backend/issues/172) by @amitwork9019
- Core. Fixes full name and email search in Customer, Drive and Appraisal APIs. [PR#189](https://github.com/imotor-code/dealer-drive-backend/pull/189) fixes [Issue#174](https://github.com/imotor-code/dealer-drive-backend/issues/174) by @amitwork9019
- Core. Allow nil value in boolean fields of options_fitted models. [PR#219](https://github.com/imotor-code/dealer-drive-backend/pull/219) by @anmoldhingra11
- Core. Fix photos upload in Vehicle Condition, Body Part Condition and Customer Rating add feature to delete photos [PR#232](https://github.com/imotor-code/dealer-drive-backend/pull/232 ) by @anmoldhingra11
- Core. Handle Dependent field cases. [PR#227](https://github.com/imotor-code/dealer-drive-backend/pull/227) by @anmoldhingra11
- Core. Allow nil default value. [PR#244](https://github.com/imotor-code/dealer-drive-backend/pull/244) by @anmoldhingra11
- Core. Make build_year optional for customer vehicles. [PR#241](https://github.com/imotor-code/dealer-drive-backend/pull/241) by @prateekpj97
- Core. Enhanced archive functionality for appraisals with unarchive capability. [PR#231](https://github.com/imotor-code/dealer-drive-backend/pull/231) by @prateekpj97
- Core. Allow clearing vehicle details fields. [PR#248](https://github.com/imotor-code/dealer-drive-backend/pull/248) by @prateekpj97

---

## 1.0.0 (Date of Release)
