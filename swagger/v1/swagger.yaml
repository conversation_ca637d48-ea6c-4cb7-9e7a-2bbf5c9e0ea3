---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}/cancel":
    patch:
      summary: Cancel a booking
      tags:
      - Bookings
      description: Cancels a booking
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: booking cancelled
        '422':
          description: missing cancel reason
        '404':
          description: booking not found
        '401':
          description: unauthorized - invalid device
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cancel_reason:
                  type: string
              required:
              - cancel_reason
  "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}/create-drive":
    post:
      summary: Convert booking to drive
      tags:
      - Drives
      security:
      - Bearer: []
      description: Convert an existing Booking to a test_drive or loan. Converts test_drive_booking
        to test_drive and loan_booking to loan.
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Booking UUID
        schema:
          type: string
      responses:
        '201':
          description: Drive created from booking successfully
        '422':
          description: Invalid booking status
        '404':
          description: Booking not found
        '401':
          description: Unauthorized
  "/api/v1/dealerships/{dealership_uuid}/bookings":
    post:
      summary: Creates a booking
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      responses:
        '201':
          description: test drive booking created successfully
        '422':
          description: invalid time sequence (pickup >= return)
        '401':
          description: unauthorized - expired token
        '404':
          description: sales person not found
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                vehicle_uuid:
                  type: string
                drive_type:
                  type: string
                  enum:
                  - test_drive_booking
                  - loan_booking
                expected_pickup_datetime:
                  type: string
                  format: date-time
                expected_return_datetime:
                  type: string
                  format: date-time
                sales_person_uuid:
                  type: string
                customer_uuid:
                  type: string
                notes:
                  type: string
                customer_info:
                  type: object
                  properties:
                    first_name:
                      type: string
                    last_name:
                      type: string
                    email:
                      type: string
                    phone_number:
                      type: string
                    address_line1:
                      type: string
                    city:
                      type: string
                    state:
                      type: string
                    postcode:
                      type: string
                    age:
                      type: integer
                    gender:
                      type: string
                      enum:
                      - unspecified
                      - male
                      - female
                      - other
                    company_name:
                      type: string
                    suburb:
                      type: string
                    address_line2:
                      type: string
                    country:
                      type: string
                    driver_license:
                      type: object
                      properties:
                        licence_number:
                          type: string
                        expiry_date:
                          type: string
                        issuing_state:
                          type: string
                        issuing_country:
                          type: string
                        category:
                          type: string
                        issue_date:
                          type: string
                        full_name:
                          type: string
                        date_of_birth:
                          type: string
                        front_image:
                          type: string
                          format: binary
                        back_image:
                          type: string
                          format: binary
              required:
              - vehicle_uuid
              - drive_type
              - expected_pickup_datetime
              - expected_return_datetime
              - sales_person_uuid
    get:
      summary: Get list of bookings
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      - name: status
        in: query
        required: false
        enum:
        - scheduled
        - completed
        - cancelled
        description: "Filter bookings by status:\n * `scheduled` \n * `completed`
          \n * `cancelled` \n "
        schema:
          type: string
      - name: drive_type
        in: query
        required: false
        enum:
        - test_drive_booking
        - loan_booking
        description: 'Filter bookings by drive type: test_drive_booking, loan_booking'
        schema:
          type: string
      - name: vehicle_uuid
        in: query
        required: false
        description: Filter bookings by vehicle UUID
        schema:
          type: string
      - name: sales_person_uuid
        in: query
        required: false
        description: Filter bookings by sales person UUID
        schema:
          type: string
      - name: start_date
        in: query
        required: false
        format: date
        description: Filter bookings from this date
        schema:
          type: string
      - name: end_date
        in: query
        required: false
        format: date
        description: Filter bookings until this date
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number for pagination (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Number of items per page (default: 20, max: 100)'
        schema:
          type: integer
      responses:
        '200':
          description: bookings with pagination
        '401':
          description: unauthorized - Invalid device
  "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}":
    get:
      summary: Retrieve a specific booking by UUID
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: booking found
        '404':
          description: booking not found
        '401':
          description: unauthorized - Invalid device
    put:
      summary: Update an existing booking
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: booking updated successfully
        '422':
          description: invalid input (pickup before return)
        '401':
          description: unauthorized - Invalid device
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                vehicle_uuid:
                  type: string
                sales_person_uuid:
                  type: string
                expected_pickup_datetime:
                  type: string
                  format: date-time
                expected_return_datetime:
                  type: string
                  format: date-time
                notes:
                  type: string
              description: At least one of the fields must be provided. All fields
                are optional but at least one is required.
  "/api/v1/brands":
    get:
      summary: Get all brands
      tags:
      - Brands
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID
        schema:
          type: string
      responses:
        '200':
          description: brands retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Brands retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      brands:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 123e4567-e89b-12d3-a456-************
                            name:
                              type: string
                              example: Toyota
                            logo_url:
                              type: string
                              nullable: true
                              example: https://example.com/logo.png
                          required:
                          - uuid
                          - name
                          - logo_url
                    required:
                    - brands
                required:
                - status
                - data
        '401':
          description: unauthorized - missing device ID
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/agreements":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: Retrieves dealership agreement texts
      tags:
      - Dealerships
      description: Retrieves agreement texts (test drive terms, car loan terms, insurance
        waiver) for a specific dealership
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: 'Bearer token in the format: Bearer <token>'
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for authentication
        schema:
          type: string
      responses:
        '200':
          description: Agreement texts retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Agreement texts retrieved successfully
                  data:
                    type: object
                    properties:
                      agreements:
                        type: object
                        properties:
                          test_drive_terms_text:
                            type:
                            - string
                            - 'null'
                            example: Standard test drive terms and conditions apply.
                          car_loan_terms_text:
                            type:
                            - string
                            - 'null'
                            example: Standard car loan terms and conditions apply.
                          insurance_waiver_text:
                            type:
                            - string
                            - 'null'
                            example: Standard insurance waiver terms and conditions
                              apply.
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
        '401':
          description: Missing Device-ID header
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
  "/api/v1/dealerships/{dealership_uuid}/valuers":
    get:
      summary: Get appraisal valuers for a dealership
      tags:
      - Appraisal Valuers
      security:
      - Bearer: []
      description: Retrieves all active appraisal valuers for a specific dealership
        with pagination support
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for authentication
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: UUID of the dealership
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number for pagination (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Number of items per page (default: 20, max: 100)'
        schema:
          type: integer
      responses:
        '200':
          description: Appraisal valuers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisal valuers retrieved successfully
                  data:
                    type: object
                    properties:
                      valuers:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 123e4567-e89b-12d3-a456-************
                            business_name:
                              type: string
                              example: ABC Auto Valuers
                            email:
                              type: string
                              example: <EMAIL>
                            first_name:
                              type: string
                              example: John
                            last_name:
                              type: string
                              example: Doe
                            mobile_number:
                              type: string
                              example: "+***********"
                            status:
                              type: string
                              example: active
                            full_name:
                              type: string
                              example: John Doe
                            created_at:
                              type: string
                              format: datetime
                              example: '2024-01-01T00:00:00.000Z'
                            updated_at:
                              type: string
                              format: datetime
                              example: '2024-01-01T00:00:00.000Z'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Unauthorized
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/archive":
    put:
      summary: Archive or unarchive an appraisal
      tags:
      - Appraisals
      description: Changes the appraisal status to archived or restores it from archived
        state. When archiving, the previous status is stored. When unarchiving, the
        previous status is restored.
      operationId: archiveAppraisal
      security:
      - Bearer: []
      parameters:
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: status
        in: query
        required: true
        description: true to archive, false to unarchive
        schema:
          type: boolean
      responses:
        '200':
          description: Appraisal unarchived successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisal unarchived successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - awarded
                            example: complete
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: status parameter is required
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                    required:
                    - code
                    - message
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Unauthorized
                    required:
                    - code
                    - message
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/award":
    post:
      summary: Award an appraisal offer
      tags:
      - Appraisals
      security:
      - Bearer: []
      description: Mark a specific appraisal offer as awarded, optionally update the
        offer price, and update the appraisal status to awarded
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Appraisal offer awarded with updated price
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisal offer awarded successfully
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        description: Updated appraisal with awarded status
        '401':
          description: Unauthorized
        '404':
          description: Appraisal offer not found
        '422':
          description: Offer already awarded
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: offer_uuid is required
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                offer_uuid:
                  type: string
                  description: UUID of the appraisal offer to award
                  example: 123e4567-e89b-12d3-a456-************
                offer_price:
                  type: number
                  description: New offer price (optional). If provided, updates the
                    offer price before awarding
                  example: 27000.0
              required:
              - offer_uuid
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/reconditioning-costs":
    put:
      summary: Bulk upsert reconditioning costs for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Reconditioning costs updated (with existing costs updated)
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Reconditioning costs updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  reconditioning_costs:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        cost_type:
                                          type: string
                                          example: paint_and_panel
                                        amount:
                                          type: string
                                          example: '2000.00'
                                        currency:
                                          type: string
                                          example: AUD
                              vehicle_condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  reconditioning_costs:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        cost_type:
                                          type: string
                                          example: mechanical
                                        amount:
                                          type: string
                                          example: '3500.00'
                                        currency:
                                          type: string
                                          example: AUD
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                required:
                - status
        '401':
          description: Missing authorization token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - reconditioning_costs
              properties:
                reconditioning_costs:
                  type: array
                  minItems: 1
                  items:
                    type: object
                    required:
                    - cost_type
                    - amount
                    - currency
                    properties:
                      cost_type:
                        type: string
                        enum:
                        - paint_and_panel
                        - wheels_and_tyres
                        - windscreen
                        - mechanical
                        - registration
                        - other
                        description: Type of reconditioning cost
                        example: paint_and_panel
                      amount:
                        type: number
                        format: float
                        minimum: 0
                        description: Cost amount
                        example: 2000.0
                      currency:
                        type: string
                        description: Currency code
                        example: AUD
        required: true
  "/api/v1/dealerships/{dealership_uuid}/appraisals":
    post:
      summary: Create a new appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      responses:
        '201':
          description: Appraisal created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Appraisal Created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            example: incomplete
                          vehicle:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2023
                              color:
                                type: string
                                example: White
                              rego:
                                type: string
                                example: ABC123
                              display_name:
                                type: string
                                example: 2023 Toyota Camry
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+***********"
                              full_name:
                                type: string
                                example: John Doe
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Jane Smith
                          updated_by:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Johnson
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Bob Johnson
                          created_by:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Johnson
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Bob Johnson
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Missing customer_uuid
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: customer_uuid is required
                required:
                - status
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                required:
                - status
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - customer_uuid
              properties:
                customer_uuid:
                  type: string
                  format: uuid
                  example: 550e8400-e29b-41d4-a716-************
                  description: Customer UUID
        required: true
    get:
      summary: List appraisals
      tags:
      - Appraisals
      description: Retrieves a list of appraisals for a dealership with optional filtering
      operationId: listAppraisals
      security:
      - Bearer: []
      parameters:
      - name: dealership_uuid
        in: path
        description: Dealership UUID
        required: true
        schema:
          type: string
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: status
        in: query
        required: false
        description: "Filter by status:\n * `incomplete` \n * `complete` \n * `awarded`
          \n * `archived` \n * `deleted` \n "
        enum:
        - incomplete
        - complete
        - awarded
        - archived
        - deleted
        schema:
          type: string
      - name: customer_uuid
        in: query
        required: false
        description: Filter by customer UUID
        schema:
          type: string
      - name: salesperson_uuid
        in: query
        required: false
        description: Filter by salesperson UUID
        schema:
          type: string
      - name: brand_uuid
        in: query
        required: false
        description: Filter by vehicle brand UUID
        schema:
          type: string
      - name: registration_number
        in: query
        required: false
        description: Filter by vehicle registration number
        schema:
          type: string
      - name: start_date
        in: query
        required: false
        description: Filter by start date (YYYY-MM-DD)
        schema:
          type: string
      - name: end_date
        in: query
        required: false
        description: Filter by end date (YYYY-MM-DD)
        schema:
          type: string
      - name: only_mine
        in: query
        required: false
        description: Filter to current user appraisals
        schema:
          type: string
      - name: query
        in: query
        required: false
        description: Search term for customer name, email, phone, vehicle details
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: Page number
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: Items per page
        schema:
          type: integer
      responses:
        '200':
          description: Appraisals retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisals retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisals:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              format: uuid
                            status:
                              type: string
                              enum:
                              - incomplete
                              - complete
                              - awarded
                              - archived
                              - deleted
                            customer:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  format: uuid
                                first_name:
                                  type: string
                                last_name:
                                  type: string
                                email:
                                  type: string
                                  format: email
                                phone_number:
                                  type: string
                                full_name:
                                  type: string
                            sales_person:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  format: uuid
                                first_name:
                                  type: string
                                last_name:
                                  type: string
                                email:
                                  type: string
                                  format: email
                                full_name:
                                  type: string
                            created_at:
                              type: string
                              format: date-time
                            updated_at:
                              type: string
                              format: date-time
                    required:
                    - appraisals
                required:
                - status
                - data
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle":
    post:
      summary: Create vehicle for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Vehicle created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Vehicle created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+***********"
                              full_name:
                                type: string
                                example: John Doe
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Jane Smith
                              phone:
                                type: string
                                example: "+61400000001"
                              job_title:
                                type: string
                                nullable: true
                                example: Sales Representative
                          created_by:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Johnson
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Bob Johnson
                              phone:
                                type: string
                                example: "+61400000002"
                              job_title:
                                type: string
                                nullable: true
                                example: Sales Manager
                          updated_by:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Alice
                              last_name:
                                type: string
                                example: Brown
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Alice Brown
                              phone:
                                type: string
                                example: "+61400000003"
                              job_title:
                                type: string
                                nullable: true
                                example: Sales Representative
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              vin:
                                type: string
                                example: 1HGBH41JXMN109186
                              rego:
                                type: string
                                example: ABC123
                              registration_expiry:
                                type: string
                                format: date
                                nullable: true
                              registration_state:
                                type: string
                                example: NSW
                              build_year:
                                type: integer
                                example: 2020
                              build_month:
                                type: integer
                                nullable: true
                              compliance_year:
                                type: integer
                                nullable: true
                              compliance_month:
                                type: integer
                                nullable: true
                              exterior_color:
                                type: string
                                example: White
                              interior_color:
                                type: string
                                example: Black
                              seat_type:
                                type: string
                                enum:
                                - leather
                                - cloth
                                - mixed
                                example: leather
                                nullable: true
                              fuel_type:
                                type: string
                                enum:
                                - petrol
                                - diesel
                                - electric
                                - hybrid
                                - plugin_hybrid
                                - lpg
                                - other
                                example: petrol
                                nullable: true
                              driving_wheels:
                                type: string
                                enum:
                                - fwd
                                - rwd
                                - awd
                                - four_wd
                                example: fwd
                                nullable: true
                              spare_wheel_type:
                                type: string
                                enum:
                                - full_size
                                - space_saver
                                - run_flat
                                - repair_kit
                                - no_spare_wheel
                                example: full_size
                                nullable: true
                              transmission:
                                type: string
                                enum:
                                - manual
                                - automatic
                                - cvt
                                - semi_automatic
                                - dual_clutch
                                example: automatic
                                nullable: true
                              body_type:
                                type: string
                                enum:
                                - sedan
                                - hatchback
                                - wagon
                                - suv
                                - coupe
                                - convertible
                                - ute
                                - van
                                - truck
                                - unknown_body
                                example: sedan
                                nullable: true
                              number_of_doors:
                                type: integer
                                example: 4
                              number_of_seats:
                                type: integer
                                example: 5
                              engine_kilowatts:
                                type: integer
                                nullable: true
                                example: 150
                              engine_number:
                                type: string
                                nullable: true
                              wheel_size_front:
                                type: integer
                                nullable: true
                                example: 17
                              wheel_size_rear:
                                type: integer
                                nullable: true
                                example: 17
                              odometer_reading:
                                type: integer
                                nullable: true
                                example: 50000
                              odometer_date:
                                type: string
                                format: date
                                nullable: true
                              redbook_code:
                                type: string
                                nullable: true
                              is_vehicle_present:
                                type: boolean
                                example: true
                              created_at:
                                type: string
                                format: date-time
                                example: '2023-07-01T09:00:00.000Z'
                              updated_at:
                                type: string
                                format: date-time
                                example: '2023-07-01T09:00:00.000Z'
                              main_photo_url:
                                type: string
                                nullable: true
                                example: "/rails/active_storage/blobs/redirect/abc123/main_photo.jpg"
                              odometer_reading_photo_url:
                                type: string
                                nullable: true
                                example: "/rails/active_storage/blobs/redirect/def456/odometer_photo.jpg"
                              media_files:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: integer
                                    url:
                                      type: string
                                nullable: true
                                example:
                                - id: 1
                                  url: "/rails/active_storage/blobs/redirect/abc123/photo1.jpg"
                              customer_uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              appraisal_uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              brand_uuid:
                                type: string
                                format: uuid
                                nullable: true
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00.000Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00.000Z'
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        message:
                          type: string
                          example: Make can't be blank
                required:
                - status
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                required:
                - status
        '401':
          description: Invalid device
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
                required:
                - status
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                make:
                  type: string
                  description: Vehicle make
                  example: Toyota
                model:
                  type: string
                  description: Vehicle model
                  example: Camry
                build_year:
                  type: integer
                  description: Vehicle build year
                  example: 2020
                vin:
                  type: string
                  description: Vehicle identification number
                  example: 1HGBH41JXMN109186
                rego:
                  type: string
                  description: Registration number
                  example: ABC123
                registration_state:
                  type: string
                  description: Registration state
                  example: NSW
                registration_expiry:
                  type: string
                  format: date
                  description: Registration expiry date
                build_month:
                  type: integer
                  description: Vehicle build month (1-12)
                compliance_year:
                  type: integer
                  description: Compliance year
                compliance_month:
                  type: integer
                  description: Compliance month (1-12)
                exterior_color:
                  type: string
                  description: Exterior color
                  example: White
                interior_color:
                  type: string
                  description: Interior color
                  example: Black
                engine_kilowatts:
                  type: integer
                  description: Engine power in kilowatts
                  example: 150
                engine_number:
                  type: string
                  description: Engine number
                engine_size:
                  type: string
                  description: Engine size
                  example: 2.0L
                wheel_size:
                  type: string
                  description: Wheel size
                  example: 17 inch
                wheel_size_front:
                  type: integer
                  description: Front wheel size in inches
                  example: 17
                wheel_size_rear:
                  type: integer
                  description: Rear wheel size in inches
                  example: 17
                odometer_reading:
                  type: integer
                  description: Odometer reading
                  example: 50000
                odometer_date:
                  type: string
                  format: date
                  description: Odometer reading date
                redbook_code:
                  type: string
                  description: Redbook code
                seat_type:
                  type: string
                  enum:
                  - leather
                  - cloth
                  - mixed
                  description: Seat type
                fuel_type:
                  type: string
                  enum:
                  - petrol
                  - diesel
                  - electric
                  - hybrid
                  - plugin_hybrid
                  - lpg
                  - other
                  description: Fuel type
                driving_wheels:
                  type: string
                  enum:
                  - fwd
                  - rwd
                  - awd
                  - four_wd
                  description: Driving wheels
                spare_wheel_type:
                  type: string
                  enum:
                  - full_size
                  - space_saver
                  - run_flat
                  - repair_kit
                  - no_spare_wheel
                  description: Spare wheel type
                transmission:
                  type: string
                  enum:
                  - manual
                  - automatic
                  - cvt
                  - semi_automatic
                  - dual_clutch
                  description: Transmission type
                body_type:
                  type: string
                  enum:
                  - sedan
                  - hatchback
                  - wagon
                  - suv
                  - coupe
                  - convertible
                  - ute
                  - van
                  - truck
                  - unknown_body
                  description: Body type
                number_of_doors:
                  type: integer
                  description: Number of doors
                  example: 4
                number_of_seats:
                  type: integer
                  description: Number of seats
                  example: 5
                is_vehicle_present:
                  type: boolean
                  description: Is vehicle present for inspection
                main_photo:
                  type: string
                  format: binary
                  description: Main vehicle photo
                odometer_reading_photo:
                  type: string
                  format: binary
                  description: Odometer reading photo
                media_files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Multiple vehicle media_files (max 5)
                  maxItems: 5
              required:
              - make
              - model
              - build_year
    put:
      summary: Update vehicle for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Partial update successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Vehicle updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          vehicle:
                            type: object
                            properties:
                              seat_type:
                                type: string
                                nullable: true
                                enum:
                                - leather
                                - cloth
                                - mixed
                                example: leather
                              fuel_type:
                                type: string
                                nullable: true
                                enum:
                                - petrol
                                - diesel
                                - electric
                                - hybrid
                                - plugin_hybrid
                                - lpg
                                - other
                                example: petrol
                              brand:
                                type: object
                                nullable: true
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Honda
                              model:
                                type: string
                                example: Civic
                              vin:
                                type: string
                                example: 1HGBH41JXMN109186
                              rego:
                                type: string
                                example: ABC123
                              registration_expiry:
                                type: string
                                format: date
                                nullable: true
                              registration_state:
                                type: string
                                example: NSW
                              build_year:
                                type: integer
                                example: 2021
                              build_month:
                                type: integer
                                nullable: true
                              compliance_year:
                                type: integer
                                nullable: true
                              compliance_month:
                                type: integer
                                nullable: true
                              exterior_color:
                                type: string
                                example: Red
                              interior_color:
                                type: string
                                example: Black
                              driving_wheels:
                                type: string
                                enum:
                                - fwd
                                - rwd
                                - awd
                                - four_wd
                                example: fwd
                              spare_wheel_type:
                                type: string
                                enum:
                                - full_size
                                - space_saver
                                - run_flat
                                - repair_kit
                                - no_spare_wheel
                                example: full_size
                              transmission:
                                type: string
                                enum:
                                - manual
                                - automatic
                                - cvt
                                - semi_automatic
                                - dual_clutch
                                example: automatic
                              body_type:
                                type: string
                                enum:
                                - sedan
                                - hatchback
                                - wagon
                                - suv
                                - coupe
                                - convertible
                                - ute
                                - van
                                - truck
                                - unknown_body
                                example: sedan
                              number_of_doors:
                                type: integer
                                example: 4
                              number_of_seats:
                                type: integer
                                example: 5
                              engine_kilowatts:
                                type: integer
                                nullable: true
                                example: 150
                              engine_number:
                                type: string
                                nullable: true
                              wheel_size_front:
                                type: integer
                                nullable: true
                                example: 17
                              wheel_size_rear:
                                type: integer
                                nullable: true
                                example: 17
                              odometer_reading:
                                type: integer
                                nullable: true
                                example: 60000
                              odometer_date:
                                type: string
                                format: date
                                nullable: true
                              redbook_code:
                                type: string
                                nullable: true
                              is_vehicle_present:
                                type: boolean
                                example: true
                              created_at:
                                type: string
                                format: date-time
                                example: '2023-07-01T09:00:00.000Z'
                              updated_at:
                                type: string
                                format: date-time
                                example: '2023-07-01T09:00:00.000Z'
                              main_photo_url:
                                type: string
                                nullable: true
                                example: "/rails/active_storage/blobs/redirect/abc123/main_photo.jpg"
                              odometer_reading_photo_url:
                                type: string
                                nullable: true
                                example: "/rails/active_storage/blobs/redirect/def456/odometer_photo.jpg"
                              media_files:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: integer
                                    url:
                                      type: string
                                nullable: true
                                example:
                                - id: 1
                                  url: "/rails/active_storage/blobs/redirect/abc123/photo1.jpg"
                              customer_uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              appraisal_uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              brand_uuid:
                                type: string
                                format: uuid
                                nullable: true
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+***********"
                              full_name:
                                type: string
                                example: John Doe
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Jane Smith
                              phone:
                                type: string
                                example: "+61400000001"
                              job_title:
                                type: string
                                nullable: true
                                example: Sales Representative
                          created_by:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Johnson
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Bob Johnson
                              phone:
                                type: string
                                example: "+61400000002"
                              job_title:
                                type: string
                                nullable: true
                                example: Sales Manager
                          updated_by:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Alice
                              last_name:
                                type: string
                                example: Brown
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Alice Brown
                              phone:
                                type: string
                                example: "+61400000003"
                              job_title:
                                type: string
                                nullable: true
                                example: Sales Representative
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00.000Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00.000Z'
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                required:
                - status
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                required:
                - status
        '401':
          description: Invalid device
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
                required:
                - status
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                make:
                  type: string
                  description: Vehicle make
                  example: Honda
                model:
                  type: string
                  description: Vehicle model
                  example: Civic
                build_year:
                  type: integer
                  description: Vehicle build year
                  example: 2021
                vin:
                  type: string
                  description: Vehicle identification number
                  example: 1HGBH41JXMN109186
                rego:
                  type: string
                  description: Registration number
                  example: ABC123
                registration_state:
                  type: string
                  description: Registration state
                  example: NSW
                registration_expiry:
                  type: string
                  format: date
                  description: Registration expiry date
                build_month:
                  type: integer
                  description: Vehicle build month (1-12)
                compliance_year:
                  type: integer
                  description: Compliance year
                compliance_month:
                  type: integer
                  description: Compliance month (1-12)
                exterior_color:
                  type: string
                  description: Exterior color
                  example: Red
                interior_color:
                  type: string
                  description: Interior color
                  example: Black
                engine_kilowatts:
                  type: integer
                  description: Engine power in kilowatts
                  example: 150
                engine_number:
                  type: string
                  description: Engine number
                engine_size:
                  type: string
                  description: Engine size
                  example: 2.0L
                wheel_size:
                  type: string
                  description: Wheel size
                  example: 17 inch
                wheel_size_front:
                  type: integer
                  description: Front wheel size in inches
                  example: 17
                wheel_size_rear:
                  type: integer
                  description: Rear wheel size in inches
                  example: 17
                odometer_reading:
                  type: integer
                  description: Odometer reading
                  example: 60000
                odometer_date:
                  type: string
                  format: date
                  description: Odometer reading date
                redbook_code:
                  type: string
                  description: Redbook code
                seat_type:
                  type: string
                  enum:
                  - leather
                  - cloth
                  - mixed
                  description: Seat type
                fuel_type:
                  type: string
                  enum:
                  - petrol
                  - diesel
                  - electric
                  - hybrid
                  - plugin_hybrid
                  - lpg
                  - other
                  description: Fuel type
                driving_wheels:
                  type: string
                  enum:
                  - fwd
                  - rwd
                  - awd
                  - four_wd
                  description: Driving wheels
                spare_wheel_type:
                  type: string
                  enum:
                  - full_size
                  - space_saver
                  - run_flat
                  - repair_kit
                  - no_spare_wheel
                  description: Spare wheel type
                transmission:
                  type: string
                  enum:
                  - manual
                  - automatic
                  - cvt
                  - semi_automatic
                  - dual_clutch
                  description: Transmission type
                body_type:
                  type: string
                  enum:
                  - sedan
                  - hatchback
                  - wagon
                  - suv
                  - coupe
                  - convertible
                  - ute
                  - van
                  - truck
                  - unknown_body
                  description: Body type
                number_of_doors:
                  type: integer
                  description: Number of doors
                  example: 4
                number_of_seats:
                  type: integer
                  description: Number of seats
                  example: 5
                is_vehicle_present:
                  type: boolean
                  description: Is vehicle present for inspection
                main_photo:
                  type: string
                  format: binary
                  description: Main vehicle photo
                odometer_reading_photo:
                  type: string
                  format: binary
                  description: Odometer reading photo
                media_files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Multiple vehicle media_files (max 5)
                  maxItems: 5
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/signature":
    put:
      summary: Attach customer signature to appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      description: Upload customer signature image file for an appraisal. The request
        body should contain a 'signature' field with the image file.
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer signature uploaded successfully
        '422':
          description: Invalid signature file
        '404':
          description: Appraisal not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: file
        required: true
        description: Customer signature image file (PNG/JPEG, max 5MB)
  "/api/v1/dealerships/{dealership_uuid}/appraisals/dashboard":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: Get dashboard data for appraisals
      tags:
      - Appraisals
      description: Retrieves dashboard statistics for appraisals including total count,
        completed, pending, and user favourites
      operationId: getAppraisalsDashboard
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Dashboard data retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      total_appraisals:
                        type: integer
                        example: 10
                        description: Total number of appraisals
                      completed:
                        type: integer
                        example: 3
                        description: Number of completed appraisals
                      pending:
                        type: integer
                        example: 5
                        description: Number of pending appraisals
                      awarded:
                        type: integer
                        example: 1
                        description: Number of awarded appraisals
                      archived:
                        type: integer
                        example: 1
                        description: Number of archived appraisals
                      my_favourites:
                        type: integer
                        example: 2
                        description: Number of appraisals favourited by current user
                    required:
                    - total_appraisals
                    - completed
                    - pending
                    - my_favourites
                required:
                - status
                - data
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing or invalid authorization token
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}":
    delete:
      summary: Delete an appraisal
      tags:
      - Appraisals
      description: Marks an appraisal as deleted (soft delete). The appraisal will
        no longer be accessible via normal queries but remains in the database for
        audit purposes.
      operationId: deleteAppraisal
      security:
      - Bearer: []
      parameters:
      - name: dealership_uuid
        in: path
        description: Dealership UUID
        required: true
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        description: Appraisal UUID
        required: true
        schema:
          type: string
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Appraisal deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisal deleted successfully
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: appraisal_uuid
      in: path
      description: Appraisal UUID
      required: true
      schema:
        type: string
    get:
      summary: Retrieve a specific appraisal
      tags:
      - Appraisals
      description: Retrieves detailed information about a specific appraisal including
        customer, vehicle, and dealership data
      operationId: getAppraisal
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Appraisal retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisal retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - awarded
                            - archived
                            - deleted
                            example: complete
                          completed_percentage:
                            type: integer
                            minimum: 0
                            maximum: 100
                            example: 85
                          awarded_value:
                            type: string
                            example: '25000.0'
                            description: Decimal value as string
                          price:
                            type: string
                            example: '30000.0'
                            description: Decimal value as string
                          given_price:
                            type: string
                            example: '28000.0'
                            description: Decimal value as string
                            nullable: true
                          awarded_notes:
                            type: string
                            example: Excellent condition vehicle
                            nullable: true
                          notes:
                            type: string
                            example: Additional appraisal notes
                            nullable: true
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
    put:
      summary: Update an existing appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      description: Update appraisal fields including projected arrival date and appraisal
        status. At least one field must be provided.
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Appraisal updated successfully
        '422':
          description: Invalid input
        '404':
          description: Appraisal not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                projected_arrival_date:
                  type: string
                  format: date
                  description: Expected arrival date for the vehicle
                appraisal_status:
                  type: string
                  enum:
                  - retail
                  - wholesale
                  - lost
                  description: Appraisal status classification
                awarded_value:
                  type: number
                  description: Final awarded value
                price:
                  type: number
                  description: Appraisal price
                given_price:
                  type: number
                  description: Give price for the vehicle
                awarded_notes:
                  type: string
                  maxLength: 1000
                  description: Notes about the award
                notes:
                  type: string
                  maxLength: 1000
                  description: General appraisal notes
              description: At least one field must be provided. All fields are optional
                but at least one is required.
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/favourite":
    put:
      summary: Mark or unmark appraisal as favourite
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Favourite status updated successfully
        '422':
          description: Invalid input
        '404':
          description: Appraisal not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - status
              properties:
                status:
                  type: boolean
                  example: true
                  description: true to add to favourites, false to remove
        required: true
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/offers":
    get:
      summary: Get offers for an appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      description: Retrieve all offers for a specific appraisal with detailed valuer
        information
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Offers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Offers retrieved successfully
                  data:
                    type: object
                    properties:
                      offers:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              format: uuid
                              example: 550e8400-e29b-41d4-a716-************
                            valuer_name:
                              type: string
                              example: John Smith
                            valuer_business_name:
                              type: string
                              example: Smith Valuations
                            valuer_email:
                              type: string
                              format: email
                              example: <EMAIL>
                            valuer_mobile_number:
                              type: string
                              example: "+***********"
                            offer_date:
                              type: string
                              format: date
                              example: '2024-01-15'
                            offer_price:
                              type: string
                              example: '25000.0'
                              description: Decimal value as string
                            offer_notes:
                              type: string
                              example: Vehicle in excellent condition
                            valuer_uuid:
                              type: string
                              format: uuid
                              example: 550e8400-e29b-41d4-a716-************
                              nullable: true
                            is_verbal_offer:
                              type: boolean
                              example: false
                            verbal_offer_from_user:
                              type: string
                              example: Jane Doe
                              nullable: true
                            is_awarded:
                              type: boolean
                              example: false
                            awarded_at:
                              type: string
                              format: datetime
                              example: '2024-01-15T10:30:00Z'
                              nullable: true
                            is_internal_offer:
                              type: boolean
                              example: false
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: Unauthorized
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: Not Found
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/reassign":
    put:
      summary: Reassign appraisal to another sales person
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Appraisal reassigned successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Appraisal reassigned successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - awarded
                            - archived
                            - deleted
                            example: incomplete
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
        '422':
          description: Invalid input
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: sales_person_uuid is required
                    required:
                    - code
                    - message
        '404':
          description: Not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                    required:
                    - code
                    - message
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Unauthorized
                    required:
                    - code
                    - message
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - sales_person_uuid
              properties:
                sales_person_uuid:
                  type: string
                  format: uuid
                  description: UUID of the new sales person to assign to the appraisal
                  example: 123e4567-e89b-12d3-a456-************
        required: true
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/send-for-offers":
    post:
      summary: Send offers for an appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Offers sent successfully
        '422':
          description: Duplicate valuer email error
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - offers
              properties:
                offers:
                  type: array
                  items:
                    type: object
                    properties:
                      offer_price:
                        type: number
                        example: 25000.0
                      offer_notes:
                        type: string
                        example: Good condition vehicle
                      is_internal:
                        type: boolean
                        example: false
                      appraisal_valuer_uuid:
                        type: string
                        example: '1'
                      is_verbal:
                        type: boolean
                        example: true
                      valuer_business_name:
                        type: string
                        example: ABC Valuers
                      valuer_email:
                        type: string
                        example: <EMAIL>
                      valuer_first_name:
                        type: string
                        example: John
                      valuer_last_name:
                        type: string
                        example: Doe
                      valuer_mobile_number:
                        type: string
                        example: "+***********"
                      add_valuer:
                        type: boolean
                        example: false
        required: true
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/customer":
    put:
      summary: Update customer association in appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      description: Update customer association within an appraisal by providing an
        existing customer UUID. This also updates the customer reference in the associated
        customer vehicle.
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer association updated successfully
        '422':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Customer not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - customer_uuid
              properties:
                customer_uuid:
                  type: string
                  format: uuid
                  example: 550e8400-e29b-41d4-a716-************
                  description: UUID of the existing customer to associate with this
                    appraisal
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/body-part-condition":
    put:
      summary: Upsert body part condition for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Body part condition with media_files created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Body part condition created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  body_part_conditions:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        part_name:
                                          type: string
                                          example: boot
                                        condition:
                                          type: string
                                          example: scratch
                                        description:
                                          type: string
                                          example: major
                    required:
                    - appraisal
                required:
                - status
                - data
        '200':
          description: Body part condition updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Body part condition updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  body_part_conditions:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        part_name:
                                          type: string
                                          example: bonnet
                                        condition:
                                          type: string
                                          example: dent
                                        description:
                                          type: string
                                          example: Updated condition
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                required:
                - status
        '401':
          description: Missing authorization token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
              - part_name
              - condition
              properties:
                part_name:
                  type: string
                  enum:
                  - front_bumper
                  - front_fender_skirt
                  - front_panel
                  - left_front_headlamp
                  - right_front_headlamp
                  - bonnet
                  - left_front_fender
                  - right_front_fender
                  - left_front_tyre
                  - left_front_wheel
                  - right_front_tyre
                  - right_front_wheel
                  - front_windshield
                  - left_front_door
                  - left_front_window
                  - left_running_board
                  - left_rear_window
                  - left_rear_door
                  - left_rear_fender
                  - left_rear_tyre
                  - left_rear_wheel
                  - right_front_door
                  - right_front_window
                  - right_running_board
                  - right_rear_window
                  - right_rear_door
                  - right_rear_fender
                  - right_rear_tyre
                  - right_rear_wheel
                  - rear_windshield
                  - boot
                  - left_rear_headlamp
                  - right_rear_headlamp
                  - rear_grill
                  - roof
                  - rear_fender_skirt
                  - rear_bumper
                  description: Body part name
                  example: front_bumper
                condition:
                  type: string
                  enum:
                  - okay
                  - scratch
                  - chip
                  - dent
                  - hail
                  - damaged
                  - acceptable
                  - unacceptable
                  description: Body part condition
                  example: scratch
                description:
                  type: string
                  description: Optional description of the condition
                  example: Minor scratch on the front bumper
                media_files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: media_files for this body part
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/component-rating":
    put:
      summary: Upsert component rating for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Component rating with media_files created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Component rating created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  component_ratings:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        name:
                                          type: string
                                          example: interior
                                        rating:
                                          type: integer
                                          example: 4
                    required:
                    - appraisal
                required:
                - status
                - data
        '200':
          description: Component rating updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Component rating updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  component_ratings:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        name:
                                          type: string
                                          example: paint_work
                                        rating:
                                          type: integer
                                          example: 5
                                        media_files:
                                          type: array
                                          items:
                                            type: string
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                required:
                - status
        '401':
          description: Missing authorization token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
              - name
              - rating
              properties:
                name:
                  type: string
                  enum:
                  - front_wheels
                  - rear_wheels
                  - panel_work
                  - paint_work
                  - interior
                  - windscreen
                  - mechanical
                  description: Component name
                  example: interior
                rating:
                  type: integer
                  minimum: 1
                  maximum: 5
                  description: Component rating from 1 to 5
                  example: 3
                media_files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: media_files for this component
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/finance-details":
    put:
      summary: Upsert finance details for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Finance details created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Finance details created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              finance_details:
                                type: object
                                properties:
                                  is_financed:
                                    type: string
                                    enum:
                                    - financed
                                    - not_financed
                                    - unknown
                                    example: financed
                                  finance_company:
                                    type: string
                                    example: ABC Finance
                                  current_repayment_amount:
                                    type: string
                                    example: '450.00'
                                  terms_months:
                                    type: integer
                                    example: 60
                                  interest_rate:
                                    type: string
                                    example: '5.5'
                                  next_due_date:
                                    type: string
                                    format: date
                                    example: '2024-02-15'
                                  has_clear_title:
                                    type: boolean
                                    example: false
                                  payout_amount:
                                    type: string
                                    example: '25000.00'
                    required:
                    - appraisal
                required:
                - status
                - data
        '200':
          description: Finance details updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Finance details updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              finance_details:
                                type: object
                                properties:
                                  is_financed:
                                    type: string
                                    enum:
                                    - financed
                                    - not_financed
                                    - unknown
                                    example: financed
                                  finance_company:
                                    type: string
                                    example: XYZ Finance
                                  current_repayment_amount:
                                    type: string
                                    example: '500.00'
                                  terms_months:
                                    type: integer
                                    example: 48
                                  interest_rate:
                                    type: string
                                    example: '4.5'
                                  next_due_date:
                                    type: string
                                    format: date
                                    example: '2024-03-15'
                                  has_clear_title:
                                    type: boolean
                                    example: true
                                  payout_amount:
                                    type: string
                                    example: '22000.00'
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        message:
                          type: string
                          example: Invalid value is not included in the list
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                required:
                - status
        '401':
          description: Missing authorization token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - finance
              properties:
                finance:
                  type: object
                  properties:
                    is_financed:
                      type: string
                      enum:
                      - financed
                      - not_financed
                      - unknown
                      description: 'Whether the vehicle is currently financed. Allowed
                        values: ''financed'', ''not_financed'', ''unknown'''
                      example: financed
                    finance_company:
                      type: string
                      description: Name of the finance company
                      example: ABC Finance
                    current_repayment_amount:
                      type: number
                      format: float
                      description: Current monthly repayment amount
                      example: 450.0
                    terms_months:
                      type: integer
                      description: Finance term in months
                      example: 60
                    interest_rate:
                      type: number
                      format: float
                      description: Interest rate percentage
                      example: 5.5
                    next_due_date:
                      type: string
                      format: date
                      description: Next payment due date
                      example: '2024-02-15'
                    has_clear_title:
                      type: boolean
                      description: Whether the vehicle has a clear title
                      example: false
                    payout_amount:
                      type: number
                      format: float
                      description: Payout amount to clear the finance
                      example: 25000.0
        required: true
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/options-fitted":
    put:
      summary: Upsert options fitted for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Options fitted created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Options fitted created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              options_fitted:
                                type: object
                                properties:
                                  has_sunroof:
                                    type: boolean
                                    example: true
                                  has_tinted_windows:
                                    type: boolean
                                    example: true
                                  has_towbar:
                                    type: boolean
                                    example: false
                                  has_keyless_entry:
                                    type: boolean
                                    example: true
                                  has_bluetooth:
                                    type: boolean
                                    example: true
                                  has_extended_warranty:
                                    type: boolean
                                    example: true
                                  extended_warranty_expiry:
                                    type: string
                                    format: date
                                    example: '2025-12-31'
                                  sunroof_type:
                                    type: string
                                    example: panoramic
                                  number_of_keys:
                                    type: integer
                                    example: 2
                                  heated_seats:
                                    type: boolean
                                    example: true
                                  on_written_off_register:
                                    type: string
                                    example: 'no'
                                  notes:
                                    type:
                                    - string
                                    - 'null'
                                    example: Vehicle has premium sound system and
                                      custom modifications
                                  options_images:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        url:
                                          type: string
                    required:
                    - appraisal
                required:
                - status
                - data
        '200':
          description: Options fitted updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Options fitted updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              options_fitted:
                                type: object
                                properties:
                                  has_sunroof:
                                    type: boolean
                                    example: false
                                  has_tinted_windows:
                                    type: boolean
                                    example: false
                                  has_towbar:
                                    type: boolean
                                    example: true
                                  has_keyless_entry:
                                    type: boolean
                                    example: false
                                  has_bluetooth:
                                    type: boolean
                                    example: false
                                  has_extended_warranty:
                                    type: boolean
                                    example: false
                                  sunroof_type:
                                    type: string
                                    example: standard_metal
                                  number_of_keys:
                                    type: integer
                                    example: 1
                                  heated_seats:
                                    type: boolean
                                    example: false
                                  on_written_off_register:
                                    type: string
                                    example: unknown
                                  options_images:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        url:
                                          type: string
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                required:
                - status
        '401':
          description: Missing authorization token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                has_sunroof:
                  type: boolean
                  description: Whether the vehicle has a sunroof
                  example: true
                has_tinted_windows:
                  type: boolean
                  description: Whether the vehicle has tinted windows
                  example: true
                has_towbar:
                  type: boolean
                  description: Whether the vehicle has a towbar
                  example: false
                has_keyless_entry:
                  type: boolean
                  description: Whether the vehicle has keyless entry
                  example: true
                has_bluetooth:
                  type: boolean
                  description: Whether the vehicle has bluetooth
                  example: true
                has_ventilated_seats:
                  type: boolean
                  description: Whether the vehicle has ventilated seats
                  example: false
                has_tray_fitted:
                  type: boolean
                  description: Whether the vehicle has a tray fitted
                  example: false
                has_canopy_fitted:
                  type: boolean
                  description: Whether the vehicle has a canopy fitted
                  example: false
                has_aftermarket_wheels:
                  type: boolean
                  description: Whether the vehicle has aftermarket wheels
                  example: false
                has_bull_bar:
                  type: boolean
                  description: Whether the vehicle has a bull bar
                  example: false
                has_extended_warranty:
                  type: boolean
                  description: Whether the vehicle has extended warranty
                  example: true
                extended_warranty_expiry:
                  type: string
                  format: date
                  description: Extended warranty expiry date
                  example: '2025-12-31'
                ppsr:
                  type: boolean
                  description: Whether the vehicle has PPSR
                  example: false
                additional_options:
                  type: object
                  description: Additional options as JSON object
                  example:
                    premium_sound_system: true
                    navigation: true
                sunroof_type:
                  type: string
                  enum:
                  - standard_metal
                  - standard_glass
                  - panoramic
                  description: Type of sunroof if present
                  example: panoramic
                number_of_keys:
                  type: integer
                  enum:
                  - 1
                  - 2
                  - 3
                  description: Number of keys provided
                  example: 2
                heated_seats:
                  type: boolean
                  description: Whether the vehicle has heated seats
                  example: true
                cargo_blind:
                  type: boolean
                  description: Whether the vehicle has a cargo blind
                  example: false
                tonneau_cover:
                  type: boolean
                  description: Whether the vehicle has a tonneau cover
                  example: false
                tonneau_type:
                  type: string
                  enum:
                  - hard
                  - soft
                  description: Type of tonneau cover if present
                  example: hard
                on_written_off_register:
                  type: string
                  enum:
                  - 'yes'
                  - 'no'
                  - unknown
                  description: Whether the vehicle is on written off register
                  example: 'no'
                last_ppsr_date:
                  type: string
                  format: date
                  description: Last PPSR check date
                  example: '2024-01-15'
                notes:
                  type: string
                  description: Additional notes about vehicle options
                  example: Vehicle has premium sound system and custom modifications
                options_images:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Images of vehicle options
                  maxItems: 5
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-condition":
    put:
      summary: Upsert vehicle condition for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Vehicle condition with media_files created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Vehicle condition created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_condition:
                                type: object
                                properties:
                                  uuid:
                                    type: string
                                    format: uuid
                                  is_clean:
                                    type: boolean
                                    example: true
                                  is_wet:
                                    type: boolean
                                    example: false
                                  is_road_tested:
                                    type: boolean
                                    example: true
                                  has_signs_of_repair:
                                    type: boolean
                                    example: false
                                  repair_details:
                                    type: string
                                    example: No previous repairs detected
                                  additional_notes:
                                    type: string
                                    example: Vehicle is in excellent condition
                    required:
                    - appraisal
                required:
                - status
                - data
        '200':
          description: Vehicle condition updated successfully
        '400':
          description: Invalid input
        '401':
          description: Unauthorized
        '404':
          description: Appraisal not found
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                is_clean:
                  type: boolean
                  description: Is the vehicle clean
                  example: true
                is_wet:
                  type: boolean
                  description: Is the vehicle wet
                  example: false
                is_road_tested:
                  type: boolean
                  description: Has the vehicle been road tested
                  example: true
                has_signs_of_repair:
                  type: boolean
                  description: Does the vehicle have signs of repair
                  example: false
                repair_details:
                  type: string
                  description: Details about repairs if any
                  example: No previous repairs detected
                additional_notes:
                  type: string
                  description: Additional notes about the vehicle condition
                  example: Vehicle is in excellent condition
                media_files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Condition media_files (max 5)
                  maxItems: 5
  "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-history":
    put:
      summary: Upsert vehicle history for appraisal
      tags:
      - Appraisals
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: appraisal_uuid
        in: path
        required: true
        description: Appraisal UUID
        schema:
          type: string
      responses:
        '201':
          description: Vehicle history created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Vehicle history created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_history:
                                type: object
                                properties:
                                  number_of_owners:
                                    type: integer
                                    example: 2
                                  has_accident_history:
                                    type: boolean
                                    example: false
                                  accident_details:
                                    type: string
                                    example: Minor rear-end collision in 2022
                                  last_service_date:
                                    type: string
                                    format: date
                                    example: '2024-01-15'
                                  last_service_odometer:
                                    type: integer
                                    example: 45000
                                  next_service_due:
                                    type: string
                                    format: date
                                    example: '2024-07-15'
                                  has_dash_warning_lights:
                                    type: boolean
                                    example: false
                                  dash_warning_details:
                                    type: string
                                    example: Check engine light occasionally appears
                                  notes:
                                    type: string
                                    example: Well maintained vehicle with full service
                                      history
                                  vehicle_history_status:
                                    type: string
                                    example: full_oem_history
                                  service_book_images:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        url:
                                          type: string
                    required:
                    - appraisal
                required:
                - status
                - data
        '200':
          description: Vehicle history updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Vehicle history updated successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      appraisal:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                            example: 550e8400-e29b-41d4-a716-************
                          status:
                            type: string
                            enum:
                            - incomplete
                            - complete
                            - archived
                            - deleted
                            example: incomplete
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                format: uuid
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2020
                              vehicle_history:
                                type: object
                                properties:
                                  number_of_owners:
                                    type: integer
                                    example: 3
                                  has_accident_history:
                                    type: boolean
                                    example: true
                                  accident_details:
                                    type: string
                                    example: Major front-end collision in 2023
                                  last_service_date:
                                    type: string
                                    format: date
                                    example: '2024-02-15'
                                  last_service_odometer:
                                    type: integer
                                    example: 52000
                                  next_service_due:
                                    type: string
                                    format: date
                                    example: '2024-08-15'
                                  has_dash_warning_lights:
                                    type: boolean
                                    example: true
                                  dash_warning_details:
                                    type: string
                                    example: ABS warning light on
                                  notes:
                                    type: string
                                    example: Vehicle has been repaired after accident
                                  vehicle_history_status:
                                    type: string
                                    example: partial_history
                                  service_book_images:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        id:
                                          type: integer
                                        url:
                                          type: string
                    required:
                    - appraisal
                required:
                - status
                - data
        '422':
          description: Cannot modify archived appraisal
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Cannot modify archived appraisal
                required:
                - status
        '404':
          description: Appraisal not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Appraisal not found or does not belong to this dealership
                required:
                - status
        '401':
          description: Missing authorization token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                required:
                - status
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                number_of_owners:
                  type: integer
                  description: Number of previous owners
                  example: 2
                has_accident_history:
                  type: boolean
                  description: Whether the vehicle has been in accidents
                  example: false
                accident_details:
                  type: string
                  description: Details about any accidents
                  example: Minor rear-end collision in 2022
                last_service_date:
                  type: string
                  format: date
                  description: Date of last service
                  example: '2024-01-15'
                last_service_odometer:
                  type: integer
                  description: Odometer reading at last service
                  example: 45000
                next_service_due:
                  type: string
                  description: Date when next service is due
                  example: '2024-07-15'
                has_dash_warning_lights:
                  type: boolean
                  description: Whether there are warning lights on the dashboard
                  example: false
                dash_warning_details:
                  type: string
                  description: Details about dashboard warning lights
                  example: Check engine light on
                notes:
                  type: string
                  description: Additional notes about vehicle history
                  example: Well maintained vehicle with full service history
                vehicle_history_status:
                  type: string
                  enum:
                  - no_history
                  - partial_history
                  - full_mixed_history
                  - full_oem_history
                  description: Status of vehicle history documentation
                  example: full_oem_history
                service_book_images:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Images of service book pages
                  maxItems: 5
  "/api/v1/dealerships/{dealership_uuid}/customers":
    get:
      summary: Get customers list with optional search
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Items per page (default: 20, max: 100)'
        schema:
          type: integer
      responses:
        '200':
          description: Customers retrieved successfully
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
    post:
      summary: Create customer
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer created successfully
        '422':
          description: Duplicate email error
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  description: Customer first name
                last_name:
                  type: string
                  description: Customer last name
                email:
                  type: string
                  description: Customer email
                phone_number:
                  type: string
                  description: Customer phone number
                age:
                  type: integer
                  description: Customer age
                gender:
                  type: string
                  enum:
                  - unspecified
                  - male
                  - female
                  - other
                  description: Customer gender
                company_name:
                  type: string
                  description: Company name
                external_id:
                  type: string
                  description: External ID
                postcode:
                  type: string
                  description: Postcode
                suburb:
                  type: string
                  description: Suburb
                address_line1:
                  type: string
                  description: Address line 1
                address_line2:
                  type: string
                  description: Address line 2
                city:
                  type: string
                  description: City
                state:
                  type: string
                  description: State
                country:
                  type: string
                  description: Country
              required:
              - first_name
              - last_name
              - email
              - phone_number
  "/api/v1/dealerships/{dealership_uuid}/customers/search":
    get:
      summary: Get customers list with search for autocomplete feature
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: query
        in: query
        required: false
        description: Search term (3-20 characters) for name, email, or phone
        schema:
          type: string
      responses:
        '200':
          description: Empty search results for short query
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
  "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}":
    get:
      summary: Get customer details
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer retrieved successfully
        '404':
          description: Customer not found
        '401':
          description: Unauthorized
    put:
      summary: Update customer
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer updated successfully
        '404':
          description: Customer not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  description: Customer first name
                last_name:
                  type: string
                  description: Customer last name
                email:
                  type: string
                  description: Customer email
                phone_number:
                  type: string
                  description: Customer phone number
                age:
                  type: integer
                  description: Customer age
                gender:
                  type: string
                  enum:
                  - unspecified
                  - male
                  - female
                  - other
                  description: Customer gender
                company_name:
                  type: string
                  description: Company name
                external_id:
                  type: string
                  description: External ID
                postcode:
                  type: string
                  description: Postcode
                suburb:
                  type: string
                  description: Suburb
                address_line1:
                  type: string
                  description: Address line 1
                address_line2:
                  type: string
                  description: Address line 2
                city:
                  type: string
                  description: City
                state:
                  type: string
                  description: State
                country:
                  type: string
                  description: Country
  "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}/driving-license":
    get:
      summary: Get customer's driving license
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Driving license retrieved successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
    post:
      summary: Create or update driving license
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Driving license created/updated successfully
        '422':
          description: Validation failed
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                licence_number:
                  type: string
                  example: DL123456789
                expiry_date:
                  type: string
                  format: date
                  example: '2025-12-31'
                issue_date:
                  type: string
                  format: date
                  example: '2020-01-01'
                category:
                  type: string
                  example: C
                issuing_country:
                  type: string
                  example: au
                issuing_state:
                  type: string
                  example: NSW
                full_name:
                  type: string
                  example: John Doe
                date_of_birth:
                  type: string
                  format: date
                  example: '1990-01-01'
              required:
              - licence_number
              - expiry_date
    delete:
      summary: Delete driving license
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Driving license deleted successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
  "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}/driving-license-image":
    post:
      summary: Upload driving license image
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Image uploaded successfully
        '404':
          description: Driving license not found
        '422':
          description: Invalid image type or missing file
        '401':
          description: Unauthorized
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: string
        required: true
        description: Type of image
    delete:
      summary: Delete driving license image
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      - name: image_type
        in: query
        enum:
        - front
        - back
        required: true
        description: "Type of image to delete:\n * `front` \n * `back` \n "
        schema:
          type: string
      responses:
        '200':
          description: Image deleted successfully
        '404':
          description: Driving license not found
        '422':
          description: Image not attached or invalid type
        '401':
          description: Unauthorized
  "/api/v1/dealerships/{dealership_uuid}/users":
    get:
      summary: Get dealership users
      tags:
      - Dealership Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: role_type
        in: query
        required: false
        description: "Filter by user role:\n * `dealership_admin` \n * `sales_person`
          \n * `staff` \n "
        enum:
        - dealership_admin
        - sales_person
        - staff
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: Page number
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: Items per page
        schema:
          type: integer
      responses:
        '200':
          description: Dealership users retrieved successfully
        '422':
          description: Empty results for invalid role filter
        '404':
          description: Dealership not found
        '401':
          description: Unauthorized
  "/api/v1/dealerships":
    get:
      summary: Retrieves dealerships
      tags:
      - Dealerships
      description: Retrieves a list of dealerships associated with the authenticated
        user
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: 'Bearer token in the format: Bearer <token>'
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for authentication
        schema:
          type: string
      responses:
        '200':
          description: Dealerships retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Dealerships retrieved successfully
                  data:
                    type: object
                    properties:
                      dealerships:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              format: uuid
                            name:
                              type: string
                              example: AutoNation Toyota Melbourne
                            setting_date_format:
                              type: string
                              example: dd/mm/yyyy
                            setting_time_zone:
                              type: string
                              example: Melbourne
                            setting_distance_unit:
                              type: string
                              example: kilometers
                            status:
                              type: string
                              example: active
                            address_line1:
                              type: string
                              example: 123 Collins Street
                            state:
                              type: string
                              example: Victoria
                            postcode:
                              type: string
                              example: '3000'
                            country:
                              type: string
                              example: au
                            phone:
                              type: string
                              example: "+***********"
                            email:
                              type: string
                              example: <EMAIL>
                            website:
                              type: string
                              example: https://autonation.com.au
                            long_name:
                              type:
                              - string
                              - 'null'
                              example: AutoNation Toyota Melbourne Central
                            address_line2:
                              type:
                              - string
                              - 'null'
                              example: Level 5
                            suburb:
                              type:
                              - string
                              - 'null'
                              example: Melbourne
                            external_id:
                              type:
                              - string
                              - 'null'
                            abn:
                              type:
                              - string
                              - 'null'
                              example: '***********'
                            created_at:
                              type: string
                              format: datetime
                            updated_at:
                              type: string
                              format: datetime
                            brand:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  format: uuid
                                name:
                                  type: string
                                  example: Toyota
                            dealership_features_setting:
                              type: object
                              nullable: true
                              properties:
                                advance_booking_enabled:
                                  type: boolean
                                  example: true
                                insurance_waiver_enabled:
                                  type: boolean
                                  example: true
                                dealer_drive_subscription:
                                  type: boolean
                                  example: true
                                appraisals_subscription:
                                  type: boolean
                                  example: true
                                fuel_level_in_test_drive:
                                  type: boolean
                                  example: true
                                fuel_level_in_loan:
                                  type: boolean
                                  example: true
        '401':
          description: Missing Device-ID header
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
  "/api/v1/dealerships/{dealership_uuid}/drives/{drive_uuid}/trade-plate":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: drive_uuid
      in: path
      description: Drive UUID
      required: true
      schema:
        type: string
    - name: Authorization
      in: header
      required: true
      description: Bearer token for authentication
      schema:
        type: string
    - name: Device-ID
      in: header
      required: true
      description: Device ID for the request
      schema:
        type: string
    patch:
      summary: Assign trade plate to drive
      tags:
      - Drives
      description: Assigns a trade plate to a drive. The trade plate can only be assigned
        if the vehicle associated with the drive has a blank registration value.
      security:
      - Bearer: []
      parameters: []
      responses:
        '200':
          description: Trade plate assigned successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Trade plate assigned successfully
                  data:
                    type: object
                    properties:
                      drive:
                        uuid:
                          type: string
                          example: 123e4567-e89b-12d3-a456-************
                        drive_type:
                          type: string
                          example: test_drive
                        status:
                          type: string
                          example: scheduled
                        sold_status:
                          type: string
                          example: unsold
                        notes:
                          type: string
                          nullable: true
                        expected_pickup_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        expected_return_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        start_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        end_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        start_odometer_reading:
                          type: integer
                          nullable: true
                        end_odometer_reading:
                          type: integer
                          nullable: true
                        vehicle:
                          type: object
                          properties:
                            uuid:
                              type: string
                            make:
                              type: string
                            model:
                              type: string
                            build_year:
                              type: integer
                            color:
                              type: string
                            rego:
                              type: string
                              nullable: true
                            display_name:
                              type: string
                        customer:
                          type: object
                          nullable: true
                          properties:
                            uuid:
                              type: string
                            first_name:
                              type: string
                            last_name:
                              type: string
                            email:
                              type: string
                            phone_number:
                              type: string
                            full_name:
                              type: string
                        sales_person:
                          type: object
                          properties:
                            uuid:
                              type: string
                            first_name:
                              type: string
                            last_name:
                              type: string
                            email:
                              type: string
                            full_name:
                              type: string
                        sales_person_accompanying:
                          type: object
                          nullable: true
                          properties:
                            uuid:
                              type: string
                            first_name:
                              type: string
                            last_name:
                              type: string
                            email:
                              type: string
                            full_name:
                              type: string
                        trade_plate:
                          type: object
                          nullable: true
                          properties:
                            uuid:
                              type: string
                            number:
                              type: string
                            status:
                              type: string
                            expiry:
                              type: string
                              format: date
                              nullable: true
                        created_at:
                          type: string
                          format: datetime
                        updated_at:
                          type: string
                          format: datetime
        '404':
          description: Trade plate not found or does not belong to this dealership
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        examples:
                          drive_not_found: Drive not found
                          trade_plate_not_found: Trade plate not found or does not
                            belong to this dealership
                        example: Trade plate not found
        '422':
          description: Vehicle must have blank registration
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Trade plate can only be assigned to vehicles with
                          blank or expired registration
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Email or password is incorrect.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                trade_plate_uuid:
                  type: string
                  description: UUID of the trade plate to assign
                  example: 123e4567-e89b-12d3-a456-************
              required:
              - trade_plate_uuid
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/complete":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: uuid
      in: path
      description: Drive UUID
      required: true
      schema:
        type: string
    put:
      summary: Complete a drive
      tags:
      - Drives
      description: Marks an in-progress drive as completed. Only vehicle out drive
        types (test_drive, loan, self_loan) can be completed. Updates vehicle odometer,
        location, and damage reports.
      operationId: completeDrive
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: Drive completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drive completed successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
                          status:
                            type: string
                            example: completed
                          drive_type:
                            type: string
                            example: test_drive
                          start_datetime:
                            type: string
                            format: date-time
                            example: '2024-01-15T10:00:00.000Z'
                          end_datetime:
                            type: string
                            format: date-time
                            example: '2024-01-15T12:00:00.000Z'
                          expected_pickup_datetime:
                            type: string
                            format: date-time
                            example: '2024-01-15T10:00:00.000Z'
                          expected_return_datetime:
                            type: string
                            format: date-time
                            example: '2024-01-15T12:00:00.000Z'
                          start_odometer_reading:
                            type: integer
                            example: 10000
                          end_odometer_reading:
                            type: integer
                            example: 10100
                          notes:
                            type: string
                            example: Drive completed successfully
                          sold_status:
                            type: string
                            example: unsold
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                              make:
                                type: string
                              model:
                                type: string
                              vin:
                                type: string
                              rego:
                                type: string
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                              first_name:
                                type: string
                              last_name:
                                type: string
                              email:
                                type: string
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                              first_name:
                                type: string
                              last_name:
                                type: string
                        required:
                        - uuid
                        - status
                        - drive_type
                    required:
                    - drive
                required:
                - status
                - data
        '422':
          description: Drive cannot be completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Only in-progress drives can be marked as completed
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: Drive not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Drive not found
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                notes:
                  type: string
                  description: Optional completion notes
                  example: Drive completed successfully
  "/api/v1/dealerships/{dealership_uuid}/drives":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    post:
      summary: Create a new drive
      tags:
      - Drives
      description: Creates a new drive record for a dealership. Only test_drive, loan
        and self_loan are allowed.
      operationId: createDrive
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '201':
          description: drive created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Drive Created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: 550e8400-e29b-41d4-a716-************
                          drive_type:
                            type: string
                            enum:
                            - test_drive
                            - loan
                            - self_loan
                            example: test_drive
                          status:
                            type: string
                            enum:
                            - draft
                            example: draft
                          sold_status:
                            type: string
                            enum:
                            - unsold
                            - sold
                            example: unsold
                          notes:
                            type: string
                            nullable: true
                          expected_pickup_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          expected_return_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          start_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          end_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          start_odometer_reading:
                            type: integer
                            nullable: true
                          end_odometer_reading:
                            type: integer
                            nullable: true
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2023
                              color:
                                type: string
                                example: White
                              rego:
                                type: string
                                example: ABC123
                              display_name:
                                type: string
                                example: 2023 Toyota Camry
                          customer:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+***********"
                              full_name:
                                type: string
                                example: John Doe
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Jane Smith
                          sales_person_accompanying:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Johnson
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Bob Johnson
                          trade_plate:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              number:
                                type: string
                                example: TP001
                              status:
                                type: string
                                enum:
                                - active
                                - inactive
                                example: active
                              expiry:
                                type: string
                                format: date
                                example: '2024-12-31'
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                    required:
                    - drive
                required:
                - status
                - data
        '422':
          description: invalid drive type
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Invalid Drive Type.
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing or invalid authorization token
                    required:
                    - code
                    - message
                required:
                - status
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - vehicle_uuid
              - drive_type
              properties:
                vehicle_uuid:
                  type: string
                  description: UUID of the vehicle for the drive
                drive_type:
                  type: string
                  enum:
                  - test_drive
                  - loan
                  - self_loan
                  description: Type of drive
                sales_person_uuid:
                  type: string
                  description: UUID of the sales person (optional)
    get:
      summary: List drives for a dealership
      tags:
      - Drives
      description: Retrieves all drives for a specific dealership with pagination
        and filtering options. Results are ordered by start_datetime descending.
      operationId: getDrives
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Items per page (default: 20, max: 100)'
        schema:
          type: integer
      - name: vehicle_uuid
        in: query
        required: false
        description: Filter by vehicle UUID
        schema:
          type: string
      - name: customer_uuid
        in: query
        required: false
        description: Filter by customer UUID
        schema:
          type: string
      - name: drive_type
        in: query
        required: false
        description: Filter by drive type
        schema:
          type: string
          enum:
          - test_drive
          - enquiry
          - loan
          - loan_booking
          - test_drive_booking
          - self_loan
      - name: sales_person_uuid
        in: query
        required: false
        description: Filter by sales person UUID
        schema:
          type: string
      - name: status
        in: query
        required: false
        description: Filter by drive status
        schema:
          type: string
          enum:
          - scheduled
          - in_progress
          - completed
          - cancelled
          - draft
          - deleted
      - name: sold_status
        in: query
        required: false
        description: Filter by sold status
        schema:
          type: string
          enum:
          - unsold
          - sold
      - name: trade_plate_uuid
        in: query
        required: false
        description: Filter by trade plate UUID
        schema:
          type: string
      - name: start_date_from
        in: query
        format: date
        required: false
        description: Filter by start date from (YYYY-MM-DD)
        schema:
          type: string
      - name: start_date_to
        in: query
        format: date
        required: false
        description: Filter by start date to (YYYY-MM-DD)
        schema:
          type: string
      - name: overdue
        in: query
        required: false
        description: Filter by overdue drives
        schema:
          type: boolean
      - name: updated_from
        in: query
        format: date
        required: false
        description: Filter by updated from (YYYY-MM-DD)
        schema:
          type: string
      - name: updated_to
        in: query
        format: date
        required: false
        description: Filter by updated to (YYYY-MM-DD)
        schema:
          type: string
      - name: eligible_for_return
        in: query
        required: false
        description: Filter by eligible for return
        schema:
          type: boolean
      - name: query
        in: query
        required: false
        description: Search drives by customer or vehicle details (minimum 3 characters)
        schema:
          type: string
      responses:
        '200':
          description: empty results for invalid filter
          headers:
            X-Current-Page:
              schema:
                type: string
              description: Current page number
            X-Per-Page:
              schema:
                type: string
              description: Items per page
            X-Total-Count:
              schema:
                type: string
              description: Total number of items
            X-Total-Pages:
              schema:
                type: string
              description: Total number of pages
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drives retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drives:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 550e8400-e29b-41d4-a716-************
                            drive_type:
                              type: string
                              enum:
                              - test_drive
                              - enquiry
                              - loan
                              - loan_booking
                              - test_drive_booking
                              - self_loan
                              example: test_drive
                            status:
                              type: string
                              enum:
                              - scheduled
                              - in_progress
                              - completed
                              - cancelled
                              - draft
                              - deleted
                              example: completed
                            sold_status:
                              type: string
                              enum:
                              - unsold
                              - sold
                              example: sold
                            notes:
                              type: string
                              example: Customer interested in purchasing
                              nullable: true
                            expected_pickup_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T10:00:00Z'
                              nullable: true
                            expected_return_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T12:00:00Z'
                              nullable: true
                            start_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T10:15:00Z'
                              nullable: true
                            end_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T11:45:00Z'
                              nullable: true
                            start_odometer_reading:
                              type: integer
                              example: 15000
                              nullable: true
                            end_odometer_reading:
                              type: integer
                              example: 15025
                              nullable: true
                            vehicle:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                make:
                                  type: string
                                  example: Toyota
                                model:
                                  type: string
                                  example: Camry
                                build_year:
                                  type: integer
                                  example: 2023
                                color:
                                  type: string
                                  example: Blue
                                rego:
                                  type: string
                                  example: ABC123
                                display_name:
                                  type: string
                                  example: 2023 Toyota Camry
                            customer:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                first_name:
                                  type: string
                                  example: John
                                last_name:
                                  type: string
                                  example: Doe
                                full_name:
                                  type: string
                                  example: John Doe
                                email:
                                  type: string
                                  example: <EMAIL>
                                phone_number:
                                  type: string
                                  example: "+***********"
                            sales_person:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                first_name:
                                  type: string
                                  example: Jane
                                last_name:
                                  type: string
                                  example: Smith
                                full_name:
                                  type: string
                                  example: Jane Smith
                                email:
                                  type: string
                                  example: <EMAIL>
                            sales_person_accompanying:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                first_name:
                                  type: string
                                  example: Bob
                                last_name:
                                  type: string
                                  example: Wilson
                                full_name:
                                  type: string
                                  example: Bob Wilson
                                email:
                                  type: string
                                  example: <EMAIL>
                            trade_plate:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                number:
                                  type: string
                                  example: TP001
                                expiry:
                                  type: string
                                  format: date
                                  example: '2024-12-31'
                                status:
                                  type: string
                                  enum:
                                  - active
                                  - inactive
                                  example: active
                            driver_license:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                licence_number:
                                  type: string
                                  example: DL123456789
                                full_name:
                                  type: string
                                  example: John Doe
                                expiry_date:
                                  type: string
                                  format: date
                                  example: '2025-06-30'
                            created_at:
                              type: string
                              format: date-time
                              example: '2023-07-01T09:00:00Z'
                            updated_at:
                              type: string
                              format: date-time
                              example: '2023-07-01T12:00:00Z'
                    required:
                    - drives
                required:
                - status
                - data
        '401':
          description: unauthorized
        '404':
          description: dealership not found
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/customer-signature":
    put:
      summary: Save customer signature for drive
      tags:
      - Drives
      security:
      - Bearer: []
      description: Upload customer signature image file. The request body should contain
        a 'signature' field with the image file.
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer signature uploaded successfully
        '422':
          description: Invalid signature file
        '404':
          description: Drive not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                signature:
                  type: string
                  format: binary
                  description: Customer signature image file (PNG/JPEG, max 5MB)
              required:
              - signature
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/damage-report":
    post:
      summary: Create damage report for drive
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '201':
          description: Damage report created without media files
        '422':
          description: Validation error
        '404':
          description: Drive not found
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: string
        required: true
        description: Type of damage report
  "/api/v1/dealerships/{dealership_uuid}/drives/dashboard":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: Get dashboard data for drives
      tags:
      - Drives
      description: Retrieves dashboard statistics for drives including scheduled today,
        open drives, upcoming activities, and overdue activities
      operationId: getDrivesDashboard
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Dashboard data retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      scheduled_today:
                        type: integer
                        example: 5
                        description: Number of drives scheduled for today
                      open_drives:
                        type: integer
                        example: 3
                        description: Number of drives currently in progress
                      upcoming_activities:
                        type: integer
                        example: 8
                        description: Number of drives scheduled in the next 1-3 days
                      overdue_activities:
                        type: integer
                        example: 2
                        description: Number of overdue drives
                    required:
                    - scheduled_today
                    - open_drives
                    - upcoming_activities
                    - overdue_activities
                required:
                - status
                - data
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing or invalid authorization token
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}":
    delete:
      summary: Delete a drive
      tags:
      - Drives
      description: Marks a drive as deleted (soft delete). The drive will no longer
        be accessible via normal queries but remains in the database for audit purposes.
      operationId: deleteDrive
      security:
      - Bearer: []
      parameters:
      - name: dealership_uuid
        in: path
        description: Dealership UUID
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        description: Drive UUID
        required: true
        schema:
          type: string
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Drive deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drive deleted successfully
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: Drive not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Drive not found
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: uuid
      in: path
      description: Drive UUID
      required: true
      schema:
        type: string
    get:
      summary: Get drive by ID
      tags:
      - Drives
      description: Retrieves a specific drive by its UUID for a dealership
      operationId: getDriveById
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drive retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: 550e8400-e29b-41d4-a716-************
                          drive_type:
                            type: string
                            enum:
                            - test_drive
                            - enquiry
                            - loan
                            - loan_booking
                            - test_drive_booking
                            - self_loan
                            example: test_drive
                          status:
                            type: string
                            enum:
                            - scheduled
                            - in_progress
                            - completed
                            - cancelled
                            - draft
                            - deleted
                            example: completed
                          sold_status:
                            type: string
                            enum:
                            - unsold
                            - sold
                            example: sold
                          notes:
                            type: string
                            example: Customer interested in purchasing
                            nullable: true
                          expected_pickup_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T10:00:00Z'
                            nullable: true
                          expected_return_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                            nullable: true
                          start_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T10:15:00Z'
                            nullable: true
                          end_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T11:45:00Z'
                            nullable: true
                          start_odometer_reading:
                            type: integer
                            example: 15000
                            nullable: true
                          end_odometer_reading:
                            type: integer
                            example: 15025
                            nullable: true
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2023
                              color:
                                type: string
                                example: Blue
                              rego:
                                type: string
                                example: ABC123
                              display_name:
                                type: string
                                example: 2023 Toyota Camry
                          customer:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              full_name:
                                type: string
                                example: John Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+***********"
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              full_name:
                                type: string
                                example: Jane Smith
                              email:
                                type: string
                                example: <EMAIL>
                          sales_person_accompanying:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Wilson
                              full_name:
                                type: string
                                example: Bob Wilson
                              email:
                                type: string
                                example: <EMAIL>
                          trade_plate:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              number:
                                type: string
                                example: TP001
                              expiry:
                                type: string
                                format: date
                                example: '2024-12-31'
                              status:
                                type: string
                                enum:
                                - active
                                - inactive
                                example: active
                          driver_license:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              licence_number:
                                type: string
                                example: DL123456789
                              full_name:
                                type: string
                                example: John Doe
                              expiry_date:
                                type: string
                                format: date
                                example: '2025-06-30'
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                    required:
                    - drive
                required:
                - status
                - data
        '401':
          description: unauthorized
        '404':
          description: drive not found
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/start":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: uuid
      in: path
      description: Drive UUID
      required: true
      schema:
        type: string
    put:
      summary: Start a drive
      tags:
      - Drives
      description: Transitions a drive from draft to in_progress state. Validates
        all required conditions and copies driver license data from customer.
      operationId: startDrive
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drive started successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: 123e4567-e89b-12d3-a456-************
                          drive_type:
                            type: string
                            example: test_drive
                          status:
                            type: string
                            example: in_progress
                          start_datetime:
                            type: string
                            format: date-time
                            example: '2023-01-01T10:00:00Z'
                          expected_return_datetime:
                            type: string
                            format: date-time
                            example: '2023-01-01T12:00:00Z'
                          start_odometer_reading:
                            type: integer
                            example: 10000
                          sales_person_accompanying:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                              name:
                                type: string
                          driver_license:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                              licence_number:
                                type: string
                              expiry_date:
                                type: string
                                format: date
                              full_name:
                                type: string
        '422':
          description: validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Only draft drives can be started
        '404':
          description: drive not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Drive not found
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sales_person_accompanying:
                  type: boolean
                  description: Whether sales person is accompanying the drive
                  example: true
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/customer":
    put:
      summary: Update customer for drive
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer updated successfully with new customer and driver
            license
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
                        example: Customer updated successfully
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                              first_name:
                                type: string
                              last_name:
                                type: string
                              email:
                                type: string
                              phone_number:
                                type: string
                              full_name:
                                type: string
                              driver_license:
                                type: object
                                nullable: true
                                properties:
                                  uuid:
                                    type: string
                                  licence_number:
                                    type: string
                                  expiry_date:
                                    type: string
                                    format: date
                                  issuing_state:
                                    type: string
                                  issuing_country:
                                    type: string
                                  category:
                                    type: string
                                  issue_date:
                                    type: string
                                    format: date
                                  full_name:
                                    type: string
                                  date_of_birth:
                                    type: string
                                    format: date
                                  front_image_url:
                                    type: string
                                    nullable: true
                                    description: URL of front image
                                  back_image_url:
                                    type: string
                                    nullable: true
                                    description: URL of back image
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
              - type: object
                properties:
                  customer_uuid:
                    type: string
                    description: UUID of an existing customer
                    example: 123e4567-e89b-12d3-a456-************
                required:
                - customer_uuid
              - type: object
                properties:
                  customer_info:
                    type: object
                    properties:
                      first_name:
                        type: string
                        example: John
                      last_name:
                        type: string
                        example: Doe
                      email:
                        type: string
                        example: <EMAIL>
                      phone_number:
                        type: string
                        example: "+***********"
                      age:
                        type: integer
                        example: 30
                      gender:
                        type: string
                        enum:
                        - unspecified
                        - male
                        - female
                        - other
                        example: male
                      address_line1:
                        type: string
                        example: 123 Main St
                      address_line2:
                        type: string
                        example: Apt 4B
                      suburb:
                        type: string
                        example: Richmond
                      city:
                        type: string
                        example: Melbourne
                      state:
                        type: string
                        example: VIC
                      country:
                        type: string
                        example: Australia
                      postcode:
                        type: string
                        example: '3121'
                      company_name:
                        type: string
                        example: ACME Corp
                      driver_license:
                        type: object
                        properties:
                          licence_number:
                            type: string
                            example: '12345678'
                          expiry_date:
                            type: string
                            format: date
                            example: '2025-12-31'
                          issuing_state:
                            type: string
                            example: VIC
                          category:
                            type: string
                            example: C
                          issue_date:
                            type: string
                            format: date
                            example: '2020-01-01'
                          issuing_country:
                            type: string
                            example: AU
                          full_name:
                            type: string
                            example: John Doe
                          date_of_birth:
                            type: string
                            format: date
                            example: '1990-01-01'
                          front_image:
                            type: string
                            format: binary
                            description: Front image of driver license
                          back_image:
                            type: string
                            format: binary
                            description: Back image of driver license
                    required:
                    - first_name
                    - last_name
                    - email
                    - phone_number
                required:
                - customer_info
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/location":
    post:
      summary: Update drive GPS location
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Location updated successfully
        '422':
          description: 'GPS data can only be pushed for in-progress test drives with
            accompanying sales person '
        '401':
          description: Unauthorized
        '404':
          description: Drive not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                latitude:
                  type: number
                  format: double
                  minimum: -90
                  maximum: 90
                  example: 12.9716
                  description: Latitude coordinate
                longitude:
                  type: number
                  format: double
                  minimum: -180
                  maximum: 180
                  example: 77.5946
                  description: Longitude coordinate
                accuracy:
                  type: number
                  minimum: 0
                  maximum: 1000
                  example: 5.0
                  description: Accuracy of the GPS reading in meters
              required:
              - latitude
              - longitude
        required: true
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/sold-status":
    put:
      summary: Update drive sold status
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Sold status updated successfully
        '404':
          description: Drive not found
        '422':
          description: Sold status cannot be changed
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sold_status:
                  type: string
                  enum:
                  - sold
                  - unsold
                  description: The sold status of the drive
              required:
              - sold_status
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/time":
    put:
      summary: Update drive return time
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Pickup and Return Time updated successfully for a loan booking
        '422':
          description: Invalid return time update
        '404':
          description: Drive not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - drive
              properties:
                drive:
                  type: object
                  required:
                  - expected_return_datetime
                  properties:
                    expected_return_datetime:
                      type: string
                      format: date-time
                    expected_pickup_datetime:
                      type: string
                      format: date-time
        required: true
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/odometer":
    put:
      summary: Update drive odometer reading
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Odometer/Fuel Gauge reading updated successfully
        '422':
          description: Invalid odometer update
        '404':
          description: Drive not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - drive
              properties:
                drive:
                  type: object
                  properties:
                    start_odometer_reading:
                      type: integer
                    end_odometer_reading:
                      type: integer
                    start_fuel_gauge_level:
                      type: integer
                    end_fuel_gauge_level:
                      type: integer
        required: true
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/reassign":
    put:
      summary: Reassign drive to different users
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Drive reassigned with only accompanying person
        '422':
          description: Validation error
        '404':
          description: Drive not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sales_person_uuid:
                  type: string
                  description: UUID of the new sales person to assign to the drive
                  example: 123e4567-e89b-12d3-a456-************
                sales_person_accompanying_uuid:
                  type: string
                  description: UUID of the new accompanying sales person to assign
                    to the drive
                  example: 123e4567-e89b-12d3-a456-************
        required: true
  "/api/v1/dealerships/{dealership_uuid}/trade-plates":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: List trade plates for a dealership
      tags:
      - Trade Plates
      description: Retrieves all trade plates for a specific dealership. Returns flags
        indicating if each trade plate is expired or currently in use by an in-progress
        drive.
      operationId: getTradePlates
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Trade plates retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      trade_plates:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 550e8400-e29b-41d4-a716-************
                            number:
                              type: string
                              example: TP001
                            expiry:
                              type: string
                              format: date
                              example: '2024-12-31'
                              nullable: true
                            status:
                              type: string
                              enum:
                              - active
                              - inactive
                              example: active
                            expired:
                              type: boolean
                              example: false
                              description: Flag indicating if the trade plate is expired
                            in_use:
                              type: boolean
                              example: false
                              description: Flag indicating if the trade plate is currently
                                being used by an in-progress drive
                            created_at:
                              type: string
                              format: date-time
                              example: '2023-01-01T12:00:00Z'
                            updated_at:
                              type: string
                              format: date-time
                              example: '2023-01-01T12:00:00Z'
                          required:
                          - uuid
                          - number
                          - status
                          - expired
                          - in_use
                          - created_at
                          - updated_at
                    required:
                    - trade_plates
                required:
                - status
                - data
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/vehicles":
    post:
      summary: Create a new vehicle with photo uploads
      tags:
      - Vehicles
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '201':
          description: Vehicle created successfully with media_files
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Vehicle created successfully
                  data:
                    type: object
                    properties:
                      vehicle:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                          make:
                            type: string
                            example: Toyota
                          model:
                            type: string
                            example: Camry
                          build_year:
                            type: integer
                            example: 2023
                          color:
                            type: string
                            example: Blue
                          vin:
                            type: string
                            example: 1HGBH41JXMN109186
                          stock_number:
                            type: string
                            example: TOY001
                          rego:
                            type: string
                            example: ABC123
                          status:
                            type: string
                            example: available
                          vehicle_type:
                            type: string
                            example: new_vehicle
                          media_files_count:
                            type: integer
                            example: 2
                          media_files:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: integer
                                url:
                                  type: string
                                  format: uri
                          brand:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                format: uuid
                              name:
                                type: string
                                example: Toyota
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: media_files exceeds maximum limit of 5 files
        '401':
          description: Unauthorized
        '404':
          description: Brand not found
        '400':
          description: Bad request
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                brand_uuid:
                  type: string
                  description: Brand UUID
                make:
                  type: string
                  description: Vehicle make
                model:
                  type: string
                  description: Vehicle model
                build_year:
                  type: integer
                  description: Vehicle build year
                color:
                  type: string
                  description: Vehicle color
                vin:
                  type: string
                  description: Vehicle VIN
                stock_number:
                  type: string
                  description: Stock number
                rego:
                  type: string
                  description: Registration number
                status:
                  type: string
                  enum:
                  - available
                  - in_use
                  - out_of_service
                  - sold
                  - enquiry
                vehicle_type:
                  type: string
                  enum:
                  - new_vehicle
                  - demo
                  - old
                rego_expiry:
                  type:
                  - string
                  - 'null'
                  format: date
                  description: Registration expiry date
                is_trade_plate_used:
                  type: boolean
                  description: Flag indicating if trade plate is used
                available_for_drive:
                  type: boolean
                  description: Flag indicating if vehicle is available for drive
                last_known_odometer_km:
                  type:
                  - integer
                  - 'null'
                  description: Last known odometer reading in kilometers
                last_known_fuel_gauge_level:
                  type:
                  - integer
                  - 'null'
                  description: Last known fuel gauge level
                media_files:
                  type: array
                  items:
                    type: string
                    format: binary
                    description: Vehicle photo (PNG/JPEG only, max 5MB, max 5 files)
              required:
              - make
              - model
              - build_year
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: Retrieves dealership vehicles
      tags:
      - Vehicles
      description: Retrieves a paginated list of vehicles for a specific dealership
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: 'Bearer token in the format: Bearer <token>'
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for authentication
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number for pagination (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Number of items per page (default: 20, max: 100)'
        schema:
          type: integer
      - name: vehicle_type
        in: query
        required: false
        description: "Filter by vehicle type:\n * `new_vehicle` \n * `demo` \n * `old`
          \n "
        enum:
        - new_vehicle
        - demo
        - old
        schema:
          type: string
      - name: query
        in: query
        required: false
        description: Search vehicles by stock_number, rego, make, model, color, or
          build_year (minimum 3 characters)
        schema:
          type: string
      responses:
        '200':
          description: Vehicles filtered by type
          headers:
            X-Current-Page:
              schema:
                type: string
              description: Current page number
            X-Per-Page:
              schema:
                type: string
              description: Items per page
            X-Total-Count:
              schema:
                type: string
              description: Total number of items
            X-Total-Pages:
              schema:
                type: string
              description: Total number of pages
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Dealership vehicles retrieved successfully
                  data:
                    type: object
                    properties:
                      vehicles:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              format: uuid
                            make:
                              type: string
                              example: Toyota
                            model:
                              type: string
                              example: Camry
                            build_year:
                              type: integer
                              example: 2023
                            rego:
                              type: string
                              example: ABC123
                            vin:
                              type: string
                              example: '***********234567'
                            stock_number:
                              type: string
                              example: T001
                            color:
                              type: string
                              example: Red
                            vehicle_type:
                              type: string
                              example: new_vehicle
                            status:
                              type: string
                              example: available
                            last_known_odometer_km:
                              type: integer
                              example: 10000
                            last_known_fuel_gauge_level:
                              type: integer
                              example: 75
                            display_name:
                              type: string
                              example: 2023 Toyota Camry
                            last_system_inspection_timestamp:
                              type: string
                              format: datetime
                            rego_expiry:
                              type: string
                              format: date
                            is_trade_plate_used:
                              type: boolean
                              example: false
                            available_for_drive:
                              type: boolean
                              example: true
                            created_at:
                              type: string
                              format: datetime
                            updated_at:
                              type: string
                              format: datetime
        '422':
          description: Invalid vehicle type filter
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: 'Invalid vehicle type filter. Valid filters: new_vehicle,
                          demo, old'
        '401':
          description: Missing Device-ID header
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found
  "/api/v1/devices":
    get:
      summary: List user devices
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Devices retrieved successfully
        '401':
          description: Token expired
    delete:
      summary: Logout all devices
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: All devices logged out successfully
        '401':
          description: Missing device ID
    patch:
      summary: Update device details
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Device details updated successfully
        '401':
          description: Unauthorized
        '422':
          description: Validation failed
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                device_os:
                  type: string
                  example: ios
                device_os_version:
                  type: string
                  example: 1.2.3
                app_version:
                  type: string
                  example: 1.0.0
                app_build_number:
                  type: string
                  example: 1.0.0
                fcm_token:
                  type: string
                  example: fcm-token-123
                device_name:
                  type: string
                  example: IPhone 15
  "/api/v1/auth/logout":
    delete:
      summary: Logout current device
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Device logged out successfully
        '401':
          description: Invalid device
  "/api/v1/devices/{device_id}":
    delete:
      summary: Logout a specific device
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: device_id
        in: path
        required: true
        schema:
          type: string
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Device logged out successfully
        '404':
          description: Device not found
  "/api/v1/dealerships/{dealership_uuid}/enquiries":
    post:
      summary: Creates an enquiry
      tags:
      - Enquiries
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      responses:
        '201':
          description: Enquiry created successfully
        '404':
          description: Vehicle not found
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                vehicle_uuid:
                  type: string
                  description: Vehicle UUID
                sales_person_uuid:
                  type: string
                  description: Sales person UUID (optional, defaults to current user)
                customer_uuid:
                  type: string
                  description: Existing customer UUID
                notes:
                  type: string
                  description: Enquiry notes
                customer_info:
                  type: object
                  description: New customer information (alternative to customer_uuid)
                  properties:
                    first_name:
                      type: string
                    last_name:
                      type: string
                    age:
                      type: integer
                    email:
                      type: string
                    phone_number:
                      type: string
                    gender:
                      type: string
                      enum:
                      - unspecified
                      - male
                      - female
                      - other
                    address_line1:
                      type: string
                    address_line2:
                      type: string
                    suburb:
                      type: string
                    city:
                      type: string
                    state:
                      type: string
                    country:
                      type: string
                    postcode:
                      type: string
                    company_name:
                      type: string
                    driver_license:
                      type: object
                      properties:
                        licence_number:
                          type: string
                        expiry_date:
                          type: string
                          format: date
                        issuing_state:
                          type: string
                        category:
                          type: string
                        issue_date:
                          type: string
                          format: date
                        issuing_country:
                          type: string
                        full_name:
                          type: string
                        date_of_birth:
                          type: string
                          format: date
                        front_image:
                          type: string
                          format: binary
                        back_image:
                          type: string
                          format: binary
              required:
              - vehicle_uuid
  "/api/v1/auth/login":
    post:
      summary: Login
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: Login successful, OTP sent
        '401':
          description: Missing credentials
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email:
                      type: string
                      example: <EMAIL>
                    password:
                      type: string
                      example: Drive@2025
                  required:
                  - email
                  - password
              required:
              - user
        required: true
  "/api/v1/auth/change-password":
    put:
      summary: Change Password
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: password changed
        '422':
          description: Weak new password
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - current_password
              - new_password
              properties:
                current_password:
                  type: string
                new_password:
                  type: string
  "/api/v1/profile/photo":
    delete:
      summary: Delete profile photo
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Profile photo deleted successfully
        '422':
          description: No photo attached
    put:
      summary: Update profile photo
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        example: Bearer <token>
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        example: device-123
        schema:
          type: string
      responses:
        '200':
          description: Photo updated successfully
        '422':
          description: Unsupported file type
        '401':
          description: Token has expired
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: file
        required: true
        description: Image file (JPEG/PNG)
  "/api/v1/users/me/documents/driving-license":
    get:
      summary: Get user's driving license
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Driving license retrieved successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
    post:
      summary: Create or update driving license
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Driving license created/updated successfully
        '422':
          description: Validation failed
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                licence_number:
                  type: string
                  example: DL123456789
                expiry_date:
                  type: string
                  format: date
                  example: '2025-12-31'
                issue_date:
                  type: string
                  format: date
                  example: '2020-01-01'
                category:
                  type: string
                  example: C
                issuing_country:
                  type: string
                  example: au
                issuing_state:
                  type: string
                  example: NSW
                full_name:
                  type: string
                  example: John Doe
                date_of_birth:
                  type: string
                  format: date
                  example: '1990-01-01'
              required:
              - licence_number
              - expiry_date
    delete:
      summary: Delete driving license
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Driving license deleted successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
  "/api/v1/users/me/documents/driving-license-image":
    post:
      summary: Upload driving license image
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Image uploaded successfully
        '404':
          description: Driving license not found
        '422':
          description: Invalid image type or missing file
        '401':
          description: Unauthorized
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: string
        required: true
        description: Type of image
    delete:
      summary: Delete driving license image
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: image_type
        in: query
        enum:
        - front
        - back
        required: true
        description: "Type of image to delete:\n * `front` \n * `back` \n "
        schema:
          type: string
      responses:
        '200':
          description: Image deleted successfully
        '404':
          description: Driving license not found
        '422':
          description: Image not attached or invalid type
        '401':
          description: Unauthorized
  "/api/v1/auth/forgot-password":
    post:
      summary: Forgot Password
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: reset code sent
        '404':
          description: user not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - email
              properties:
                email:
                  type: string
                  example: <EMAIL>
  "/api/v1/profile":
    get:
      summary: Get profile
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        example: Bearer <token>
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        example: device-123
        schema:
          type: string
      responses:
        '200':
          description: Profile retrieved successfully
        '401':
          description: Invalid token
    patch:
      summary: Update user profile
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Profile updated successfully
        '401':
          description: Unauthorized
        '422':
          description: Validation failed
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  example: Jane
                last_name:
                  type: string
                  example: Smith
                preferred_2fa:
                  type: string
                  enum:
                  - email
                  - sms
                  example: email
                job_title:
                  type: string
                  example: Sales Manager
                preferred_language:
                  type: string
                  example: spanish
                time_zone:
                  type: string
                  example: Brisbane
                onboarding_completed:
                  type: boolean
                  example: true
  "/api/v1/auth/refresh":
    post:
      summary: Refresh Access Token
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: Token refreshed successfully
        '401':
          description: Invalid refresh token
        '422':
          description: Missing parameters
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - refresh_token
              - device_id
              properties:
                refresh_token:
                  type: string
                  example: valid.refresh.token
                device_id:
                  type: string
                  example: device123
  "/api/v1/auth/resend-otp":
    post:
      summary: Resend OTP
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: OTP resent successfully
        '401':
          description: Invalid token
        '422':
          description: Missing method param
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - temporary_token
              - method
              properties:
                temporary_token:
                  type: string
                  example: valid.temp.token
                method:
                  type: string
                  enum:
                  - email
                  - sms
                  example: email
  "/api/v1/auth/reset-password":
    post:
      summary: Reset Password
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: password reset
        '422':
          description: missing or invalid inputs
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - temporary_token
              - new_password
              properties:
                temporary_token:
                  type: string
                  example: valid.token
                new_password:
                  type: string
                  example: NewPass@123
  "/api/v1/auth/setup-2fa":
    post:
      summary: Setup 2FA with authenticator app
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: 2FA setup initiated successfully
        '401':
          description: Unauthorized
  "/api/v1/auth/verify-2fa-setup":
    post:
      summary: Verify 2FA setup
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: 2FA successfully enabled
        '401':
          description: Invalid OTP
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                otp:
                  type: string
                  example: '123456'
                  description: 6-digit OTP code from authenticator app
              required:
              - otp
  "/api/v1/auth/verify-2fa":
    post:
      summary: Verify 2FA and complete login
      tags:
      - Authentication
      parameters:
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: App-Version
        in: header
        required: true
        description: App version
        schema:
          type: string
      - name: App-Build-Number
        in: header
        required: true
        description: App build number
        schema:
          type: string
      - name: Device-OS
        in: header
        required: true
        description: Device operating system
        schema:
          type: string
      responses:
        '200':
          description: Login successful
        '401':
          description: Missing device info
        '422':
          description: Missing required device parameters
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                temporary_token:
                  type: string
                  example: valid.temp.token
                  description: Temporary token from login response
                otp:
                  type: string
                  example: '123456'
                  description: 6-digit OTP code
                method:
                  type: string
                  example: email
                  description: 2FA method (email, sms, or totp)
              required:
              - temporary_token
              - otp
  "/api/v1/auth/request-2fa-method":
    post:
      summary: Request alternative 2FA method
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: 2FA challenge sent successfully
        '422':
          description: Invalid method
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                temporary_token:
                  type: string
                  example: valid.temp.token
                  description: Temporary token from login response
                method:
                  type: string
                  enum:
                  - sms
                  - email
                  example: email
                  description: Desired 2FA method
              required:
              - temporary_token
              - method
  "/api/v1/user_invitations":
    post:
      summary: Invite a user
      tags:
      - User Invitations
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        example: Bearer <token>
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        example: device-123
        schema:
          type: string
      responses:
        '200':
          description: Successfully invite with international phone number
        '422':
          description: Dealership UUID with a non dealership user
        '403':
          description: Dealership users can only invite other dealership users
        '401':
          description: Unauthorized when Device-ID is missing
        '404':
          description: Dealership not found
        '400':
          description: Missing required user parameter
        '500':
          description: Internal server error when service fails
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - user
              properties:
                user:
                  type: object
                  required:
                  - email
                  - first_name
                  - last_name
                  - phone
                  - user_type
                  - dealership_uuid
                  - role_type
                  properties:
                    email:
                      type: string
                      example: <EMAIL>
                    first_name:
                      type: string
                      example: Jane
                    last_name:
                      type: string
                      example: Smith
                    phone:
                      type: string
                      example: "+***********"
                    user_type:
                      type: string
                      example: dealership_user
                    dealership_uuid:
                      type: string
                      example: uuid-of-dealership
                    role_type:
                      type: string
                      example: dealership_admin
  "/api/v1/auth/verify-reset-code":
    post:
      summary: Verify Reset Code
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: code verified
        '422':
          description: missing inputs
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - temporary_token
              - reset_code
              properties:
                temporary_token:
                  type: string
                  example: temp.token
                reset_code:
                  type: string
                  example: '123456'
servers:
- url: http://localhost:{port}
  description: Development environment
  variables:
    port:
      default: '3000'
      enum:
      - '5000'
      - '4000'
      - '3000'
      - '2000'
      - '80'
- url: https://{defaultHost}
  description: Staging environment
  variables:
    defaultHost:
      default: morning-escarpment-47088-1580637df638.herokuapp.com
