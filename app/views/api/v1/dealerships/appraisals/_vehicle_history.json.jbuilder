json.vehicle_history do
  json.extract! vehicle_history,
                :uuid,
                :number_of_owners,
                :has_accident_history,
                :accident_details,
                :last_service_date,
                :last_service_odometer,
                :next_service_due,
                :has_dash_warning_lights,
                :dash_warning_details,
                :notes,
                :vehicle_history_status

  # Service book images with ids
  json.service_book_images do
    json.partial! "api/v1/shared/media_files", media_files: vehicle_history.service_book_images
  end
end
