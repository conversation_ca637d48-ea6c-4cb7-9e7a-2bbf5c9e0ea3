json.options_fitted do
  json.extract! options_fitted,
                :uuid,
                :has_sunroof,
                :has_tinted_windows,
                :has_towbar,
                :has_keyless_entry,
                :has_bluetooth,
                :has_ventilated_seats,
                :has_tray_fitted,
                :has_canopy_fitted,
                :has_aftermarket_wheels,
                :has_bull_bar,
                :has_extended_warranty,
                :extended_warranty_expiry,
                :ppsr,
                :additional_options,
                :sunroof_type,
                :number_of_keys,
                :heated_seats,
                :cargo_blind,
                :tonneau_cover,
                :tonneau_type,
                :on_written_off_register,
                :last_ppsr_date,
                :notes

  # Options images with ids
  json.options_images do
    json.partial! "api/v1/shared/media_files", media_files: options_fitted.options_images
  end
end
