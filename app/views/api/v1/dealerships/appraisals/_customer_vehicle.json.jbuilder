json.vehicle do
  # Extract basic attributes
  json.extract! customer_vehicle,
    :uuid, :make, :model, :rego, :build_year, :build_month, :exterior_color, :interior_color,
    :fuel_type, :engine_size, :wheel_size, :odometer_reading

  # Attachment URLs
  json.main_photo_url customer_vehicle.main_photo.attached? ? customer_vehicle.main_photo.url : nil

  # Brand data
  if customer_vehicle.brand.present?
    json.brand do
      json.extract! customer_vehicle.brand, :uuid, :name, :logo_url
    end
  end

  # media_files (compact view)
  json.media_files do
    json.partial! "api/v1/shared/media_files", media_files: customer_vehicle.media_files
  end
end
