# Vehicle condition data
if vehicle_condition.present?
  json.extract! vehicle_condition,
    :uuid, :is_clean, :is_wet, :is_road_tested,
    :has_signs_of_repair, :repair_details, :additional_notes

  # Condition media_files with ids
  json.media_files do
    json.partial! "api/v1/shared/media_files", media_files: vehicle_condition.media_files
  end

  # Body part conditions
  if vehicle_condition.body_part_conditions.present?
    json.body_part_conditions do
      json.array! vehicle_condition.body_part_conditions do |body_part|
        json.extract! body_part, :id, :part_name, :condition, :description
        # Add damage images if they exist (with ids)
        json.media_files do
          json.partial! "api/v1/shared/media_files", media_files: body_part.media_files
        end
      end
    end
  end

  # Component ratings
  if vehicle_condition.component_ratings.present?
    json.component_ratings do
      json.array! vehicle_condition.component_ratings do |rating|
        json.extract! rating, :id, :name, :rating
        # Add media_files if they exist (with ids)
        json.media_files do
          json.partial! "api/v1/shared/media_files", media_files: rating.media_files
        end
      end
    end
  end

  # Reconditioning costs
  if vehicle_condition.reconditioning_costs.present?
    json.reconditioning_costs do
      json.array! vehicle_condition.reconditioning_costs do |cost|
        json.extract! cost, :id, :amount, :currency, :cost_type
      end
    end
  end
end
