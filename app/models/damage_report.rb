class DamageReport < ApplicationRecord
  include HasUuid
  include HasMediaFiles

  INITIAL = 0
  FINAL = 1
  VEHICLE = 3

  belongs_to :drive, optional: true
  belongs_to :vehicle, class_name: "DealershipVehicle"

  enum :report_type, {
    initial: INITIAL,
    final: FINAL,
    vehicle: VEHICLE
  }

  # Presence validations
  validates :description, presence: true

  validate :report_type_present_for_drive

  private

  def report_type_present_for_drive
    if drive_id.present? && report_type.blank?
      errors.add(:report_type, "is needed to save damage report for drive")
    end
  end
end
