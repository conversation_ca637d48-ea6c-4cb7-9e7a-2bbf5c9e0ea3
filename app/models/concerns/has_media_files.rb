module HasMediaFiles
  extend ActiveSupport::Concern

  included do
    has_many_attached :media_files

    validate :validate_media_files_size_and_type
    validates :media_files, content_type: [ "image/png", "image/jpeg", "video/mp4", "video/quicktime" ],
                    limit: { max: 5 }
  end

  private

  def validate_media_files_size_and_type
    return unless media_files.attached?

    media_files.each do |file|
      validate_media_file_size(file)
    end
  end

  def validate_media_file_size(file)
    if file.image?
      if file.byte_size > 5.megabytes
        errors.add(:media_files, "size should not exceed 5MB for images")
      end
    elsif file.video?
      if file.byte_size > 20.megabytes
        errors.add(:media_files, "size should not exceed 20MB for videos")
      end
    else
      errors.add(:media_files, "must be an image or video")
    end
  end
end
