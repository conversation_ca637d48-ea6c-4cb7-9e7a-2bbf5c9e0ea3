class Drive < ApplicationRecord
  include HasUuid

  # Drive types where vehicle is not taken out of dealership, its just an advance booking
  BOOKING_TYPES = %w[test_drive_booking loan_booking].freeze

  # Drive types where vehicle is taken out of dealership
  VEHICLE_OUT_DRIVE_TYPES = %w[test_drive loan self_loan].freeze

  # Mapping from booking types to drive types
  BOOKING_TO_DRIVE_TYPE_MAP = {
    test_drive_booking: :test_drive,
    loan_booking: :loan
  }.freeze

  # Associations
  belongs_to :dealership
  belongs_to :vehicle, class_name: "DealershipVehicle"
  belongs_to :customer, optional: true
  belongs_to :driver_license, optional: true
  belongs_to :sales_person, class_name: "User"
  has_one :initial_damage_report, -> { where(report_type: DamageReport::INITIAL) }, class_name: "DamageReport", dependent: :destroy, inverse_of: :drive
  has_one :final_damage_report, -> { where(report_type: DamageReport::FINAL) }, class_name: "DamageReport", dependent: :destroy, inverse_of: :drive
  belongs_to :sales_person_accompanying, class_name: "User", optional: true
  belongs_to :trade_plate, optional: true
  has_many :waypoints, -> { order(created_at: :asc) }, as: :trackable, class_name: "GpsLocation", dependent: :destroy, inverse_of: :trackable
  has_one_attached :customer_signature

  accepts_nested_attributes_for :customer

  validates :end_datetime, comparison: { greater_than: :start_datetime, message: "must be after start time" }, # rubocop:disable Rails/I18nLocaleTexts
            if: -> { start_datetime.present? && end_datetime.present? }
  validates :expected_return_datetime, comparison: { greater_than: :expected_pickup_datetime, message: "must be after pickup time" }, # rubocop:disable Rails/I18nLocaleTexts
            if: -> { expected_pickup_datetime.present? && expected_return_datetime.present? }
  validate :sales_person_belongs_to_dealership

  enum :status, {
    scheduled: 0,
    in_progress: 1,
    completed: 2,
    cancelled: 3,
    draft: 4,
    deleted: 5
  }, default: :draft

  default_scope { where.not(status: :deleted) }

  enum :drive_type, {
    test_drive: 0,
    enquiry: 1,
    loan: 2,
    loan_booking: 3,
    test_drive_booking: 4,
    self_loan: 5
  }, default: :test_drive

  enum :sold_status, {
    unsold: 0,
    sold: 1
  }, default: :unsold

  scope :active, -> { where(status: :in_progress) }
  scope :bookings, -> { where(drive_type: BOOKING_TYPES) }
  scope :vehicle_out_type_drives, -> { where(drive_type: VEHICLE_OUT_DRIVE_TYPES) }

  scope :pickup_between_dates, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(expected_pickup_datetime: Date.parse(start_date).beginning_of_day..) if start_date.present?
      query = query.where(expected_pickup_datetime: ..Date.parse(end_date).end_of_day) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :updated_between_dates, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(drives: { updated_at: Date.parse(start_date).beginning_of_day.. }) if start_date.present?
      query = query.where(drives: { updated_at: ..Date.parse(end_date).end_of_day }) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :filter_by_status, ->(status) { where(status: status) }
  scope :filter_by_drive_type, ->(drive_type) { where(drive_type: drive_type) }
  scope :filter_by_sold_status, ->(sold_status) { where(sold_status: sold_status) }
  scope :overdue, -> { where("expected_return_datetime < ? AND drives.status = ?", Time.current, Drive.statuses[:in_progress]) }
  scope :by_salesperson, ->(uuid) { joins(:sales_person).where(users: { uuid: uuid }) }
  scope :by_customer, ->(uuid) { joins(:customer).where(customers: { uuid: uuid }) }
  scope :by_vehicle, ->(uuid) { joins(:vehicle).where(dealership_vehicles: { uuid: uuid }) }
  scope :by_trade_plate, ->(uuid) { joins(:trade_plate).where(trade_plates: { uuid: uuid }) }
  scope :start_datetime_between, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(start_datetime: Date.parse(start_date).beginning_of_day..) if start_date.present?
      query = query.where(start_datetime: ..Date.parse(end_date).end_of_day) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :eligible_for_return, -> {
    where(drive_type: VEHICLE_OUT_DRIVE_TYPES, status: :in_progress)
  }

  # Scopes
  scope :search_by_term, ->(term) {
    return none if term.blank?

    # Reject if less than 3 characters or more than 50 characters
    cleaned_term = term.strip
    return none if cleaned_term.length < 3 || cleaned_term.length > 50

    search_term = "%#{cleaned_term.downcase}%"
    joins(:vehicle, :customer).where(
      "LOWER(customers.first_name) LIKE :term OR
       LOWER(customers.last_name) LIKE :term OR
       LOWER(CONCAT(customers.first_name, ' ', customers.last_name)) LIKE :term OR
       LOWER(CONCAT(customers.last_name, ' ', customers.first_name)) LIKE :term OR
       LOWER(customers.email) LIKE :term OR
       LOWER(customers.phone_number) LIKE :term OR
       LOWER(dealership_vehicles.stock_number) LIKE :term OR
       LOWER(dealership_vehicles.rego) LIKE :term OR
       LOWER(dealership_vehicles.make) LIKE :term OR
       LOWER(dealership_vehicles.model) LIKE :term OR
       LOWER(dealership_vehicles.color) LIKE :term OR
       CAST(dealership_vehicles.build_year as CHAR) LIKE :term",
      term: search_term
    )
  }

  validate :validate_timestamps_not_in_future
  validate :validate_odometer_readings
  validate :validate_odometer_update_status, on: :update
  validate :validate_fuel_gauge_update_status, on: :update
  validate :validate_return_time, on: :update
  validate :validate_trade_plate_assignment, on: :update
  validates :start_odometer_reading, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :end_odometer_reading, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :start_fuel_gauge_level, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }, allow_nil: true
  validates :end_fuel_gauge_level, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }, allow_nil: true

  validates :customer_signature, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }

  def cancel_with_reason!(reason)
    raise Errors::InvalidInput, "Cancel reason is required" if reason.blank?
    raise Errors::InvalidInput, "Cancellation not allowed" if completed? || cancelled?

    self.cancel_reason = reason
    self.cancelled_at = Time.current
    self.cancelled!
  end

  def to_param
    uuid
  end

  def customer_signature_url
    return nil unless customer_signature.attached?
    customer_signature.url
  end

  def attach_customer_signature(signature_file)
    raise Errors::InvalidInput, "Signature file is required" if signature_file.blank?

    customer_signature.purge if customer_signature.attached?
    customer_signature.attach(signature_file)
  end

  def start_drive!(sales_person_accompanying, operator)
    validate_start_drive_conditions!

    ActiveRecord::Base.transaction do
      # Copy driver license data from customer to drive
      copy_driver_license_from_customer! if customer&.driver_license

      # Set start datetime
      self.start_datetime = Time.current

      # Set sales person accompanying if provided
      self.sales_person_accompanying = operator if sales_person_accompanying

      # Change status to in_progress
      self.status = :in_progress

      save!
    end
  end

  def update_sold_status!(sold_status)
    raise Errors::InvalidInput, "Invalid sold status. Must be 'sold' or 'unsold'" unless Drive.sold_statuses.key?(sold_status)

    raise Errors::InvalidInput, "Sold status cannot be changed for this drive" unless (test_drive? && completed?) || enquiry?

    self.sold_status = sold_status
    save!
  end

  def mark_completed(notes = nil)
    raise Errors::InvalidInput, "This drive cannot be marked as completed" unless VEHICLE_OUT_DRIVE_TYPES.include? drive_type

    raise Errors::InvalidInput, "Only in-progress drives can be marked as completed" unless in_progress?

    raise Errors::InvalidInput, "End odometer reading is required" if end_odometer_reading.blank?

    ActiveRecord::Base.transaction do
      self.notes = notes if notes.present?
      self.end_datetime = Time.current

      vehicle.last_known_odometer_km = end_odometer_reading
      vehicle.last_known_fuel_gauge_level = end_fuel_gauge_level if end_fuel_gauge_level.present?

      if waypoints.present?
        last_location_from_drive = waypoints.last
        vehicle_old_location = vehicle.last_known_location
        vehicle_new_location = GpsLocation.create!(
          latitude: last_location_from_drive.latitude,
          longitude: last_location_from_drive.longitude,
          accuracy: last_location_from_drive.accuracy,
          trackable: vehicle
        )
        vehicle.last_known_location = vehicle_new_location
        vehicle_old_location&.destroy!
      end
      if final_damage_report.present?
        vehicle_old_damage_report = vehicle.last_damage_report
        vehicle_new_damage_report = DamageReport.create!(
          drive: nil,
          vehicle: vehicle,
          report_type: DamageReport::VEHICLE,
          description: final_damage_report.description
        )
        vehicle.last_damage_report = vehicle_new_damage_report
        vehicle_old_damage_report&.destroy!
      end
      vehicle.save!
      self.status = :completed
      save!
      if final_damage_report.present? && final_damage_report.media_files.present? && vehicle_new_damage_report
        CopyDamageReportAttachmentsJob.perform_later(final_damage_report.id, vehicle_new_damage_report.id)
      end
    end
  end

  def create_drive!
    raise Errors::InvalidInput, "Invalid booking type" unless BOOKING_TYPES.include?(drive_type)
    raise Errors::InvalidInput, "Booking must be scheduled" unless scheduled?

    # Create new drive with copied data from booking
    new_drive = dealership.drives.create!(
      build_drive_attributes_from_booking
    )

    new_drive
  end

  private

  def build_drive_attributes_from_booking
    # Build attributes hash with associations
    {
      vehicle:,
      customer:,
      sales_person:,
      drive_type: BOOKING_TO_DRIVE_TYPE_MAP[drive_type.to_sym], # Determine the new drive type based on booking type
      status: :draft,
      notes:,
      expected_return_datetime:
    }
  end

  def validate_start_drive_conditions!
    # Drive should be of VEHICLE_OUT_DRIVE_TYPES
    unless VEHICLE_OUT_DRIVE_TYPES.include?(drive_type)
      raise Errors::InvalidInput, "Only #{VEHICLE_OUT_DRIVE_TYPES.join(', ')} drives can be started"
    end

    unless self_loan?
      # Customer should not be null, if it's not a self loan
      raise Errors::InvalidInput, "Customer is required to start the drive" if customer.blank?

      # Customer signature should be present
      unless customer_signature.attached?
        raise Errors::InvalidInput, "Customer signature is required to start the drive"
      end

      # Driver licence fields and images should not be null to start
      if customer&.driver_license
        unless customer.driver_license.licence_number.present? &&
              customer.driver_license.expiry_date.present? &&
              customer.driver_license.full_name.present? &&
              customer.driver_license.date_of_birth.present? &&
              customer.driver_license.front_image.attached? &&
              customer.driver_license.back_image.attached?
          raise Errors::InvalidInput, "Driver license is not complete"
        end
      end
    end

    # Trade plate should not be null in case vehicle.rego is blank
    if vehicle.rego.blank? && trade_plate.blank?
      raise Errors::InvalidInput, "Trade plate is required when vehicle registration is blank"
    end

    # Current status should be draft
    unless draft?
      raise Errors::InvalidInput, "Only draft drives can be started"
    end

    # Expected return datetime should not be null and should be at least 5 minutes from now
    if expected_return_datetime.blank?
      raise Errors::InvalidInput, "Expected return datetime is required"
    end

    if expected_return_datetime < 5.minutes.from_now
      raise Errors::InvalidInput, "Expected return datetime must be at least 5 minutes from now"
    end

    # End datetime should be null
    if end_datetime.present?
      raise Errors::InvalidInput, "End datetime should not be set before starting the drive"
    end

    # Start datetime should be null
    if start_datetime.present?
      raise Errors::InvalidInput, "Start datetime should not be set before starting the drive"
    end

    # Start odometer reading should not be null
    if start_odometer_reading.blank?
      raise Errors::InvalidInput, "Start odometer reading is required"
    end

    # if vehicle is already in use, don't allow
    if vehicle.currently_on_test_drive?
      raise Errors::InvalidInput, "Vehicle is already in use on another drive"
    end
  end

  def copy_driver_license_from_customer!
    return unless customer&.driver_license

    license = customer.driver_license

    # Create a new driver license for the drive with customer's data
    new_driver_license = DriverLicense.new(license.attributes.except("id", "uuid", "created_at", "updated_at"))
    new_driver_license.holder = self
    new_driver_license.save!

    # Copy attached images if they exist
    if license.front_image.attached?
      new_driver_license.front_image.attach(
        io: StringIO.new(license.front_image.download),
        filename: license.front_image.filename,
        content_type: license.front_image.content_type
      )
    end

    if license.back_image.attached?
      new_driver_license.back_image.attach(
        io: StringIO.new(license.back_image.download),
        filename: license.back_image.filename,
        content_type: license.back_image.content_type
      )
    end

    self.driver_license = new_driver_license
  end

  def validate_timestamps_not_in_future
    now = Time.current

    if start_datetime.present? && start_datetime > now
      errors.add(:start_datetime, "cannot be in the future")
    end

    if end_datetime.present? && end_datetime > now
      errors.add(:end_datetime, "cannot be in the future")
    end
  end

  def validate_odometer_readings
     return unless start_odometer_reading.present? && end_odometer_reading.present?

    if start_odometer_reading > end_odometer_reading
      errors.add(:end_odometer_reading, :invalid_sequence, message: "must be greater than start odometer reading")
    end
  end

  def sales_person_belongs_to_dealership
    return unless sales_person && dealership

    unless sales_person.dealerships.active.include?(dealership)
      errors.add(:sales_person, "must belong to the vehicle's dealership")
    end
  end

  def validate_odometer_update_status
    return unless start_odometer_reading_changed? || end_odometer_reading_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include? drive_type
      errors.add(:base, "Odometer readings can only be updated for test drives, loans, and self loans")
      return
    end

    if start_odometer_reading_changed? && !draft?
      errors.add(:start_odometer_reading, "can only be updated when drive is in draft status")
    end

    if end_odometer_reading_changed? && !in_progress?
      errors.add(:end_odometer_reading, "can only be updated when drive is in progress")
    end
  end

  def validate_fuel_gauge_update_status
    return unless start_fuel_gauge_level_changed? || end_fuel_gauge_level_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include? drive_type
      errors.add(:base, "Fuel gauge readings can only be updated for test drives, loans, and self loans")
      return
    end

    if start_fuel_gauge_level_changed? && !draft?
      errors.add(:start_fuel_gauge_level, "can only be updated when drive is in draft status")
    end

    if end_fuel_gauge_level_changed? && !in_progress?
      errors.add(:end_fuel_gauge_level, "can only be updated when drive is in progress")
    end
  end

  def validate_return_time
    return unless expected_return_datetime_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include?(drive_type) || BOOKING_TYPES.include?(drive_type)
      errors.add(:base, "Return time cannot be updated for this drive type")
    end
  end

  def validate_trade_plate_assignment
    return unless trade_plate_id_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include?(drive_type)
      errors.add(:base, "Trade plate can only be assigned to vehicle out drives")
    end

    if vehicle.rego.present? && (vehicle.rego_expiry.blank? || (vehicle.rego_expiry.present? && vehicle.rego_expiry > Date.current))
      errors.add(:base, "Trade plate can only be assigned to vehicles with blank or expired registration")
    end
  end
end
