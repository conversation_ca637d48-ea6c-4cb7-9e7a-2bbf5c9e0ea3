class Api::V1::VehicleSerializer < Api::V1::BaseSerializer
  fields :uuid, :make, :model, :build_year, :color, :vin, :stock_number, :rego,
             :rego_expiry, :status, :vehicle_type, :is_trade_plate_used, :available_for_drive,
             :last_known_odometer_km, :last_known_fuel_gauge_level, :last_system_inspection_timestamp,
             :display_name, :created_at, :updated_at

  field :media_files do |vehicle|
    vehicle.media_files.map { |file| { id: file.id, url: file.url } } if vehicle.media_files.attached?
  end

  field :media_files_count do |vehicle|
    vehicle.media_files.count
  end

  association :brand, blueprint: Api::V1::BrandSerializer
end
