require 'rails_helper'

RSpec.describe Vehicle::BodyPartCondition, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:vehicle_condition) }
    it { is_expected.to have_many_attached(:media_files) }
  end

  describe 'enums' do
    it do
      expect(described_class.part_names.keys).to include(
        'front_bumper', 'front_fender_skirt', 'front_panel', 'left_front_headlamp', 'right_front_headlamp',
        'bonnet', 'left_front_fender', 'right_front_fender', 'left_front_tyre', 'left_front_wheel',
        'right_front_tyre', 'right_front_wheel', 'front_windshield', 'left_front_door', 'left_front_window',
        'left_running_board', 'left_rear_window', 'left_rear_door', 'left_rear_fender', 'left_rear_tyre',
        'left_rear_wheel', 'right_front_door', 'right_front_window', 'right_running_board', 'right_rear_window',
        'right_rear_door', 'right_rear_fender', 'right_rear_tyre', 'right_rear_wheel', 'rear_windshield',
        'boot', 'left_rear_headlamp', 'right_rear_headlamp', 'rear_grill', 'roof', 'rear_fender_skirt', 'rear_bumper'
      )
    end
    it do
      expect(described_class.conditions.keys).to include(
        'okay', 'scratch', 'chip', 'dent', 'hail', 'damaged', 'acceptable', 'unacceptable'
      )
    end
  end

  describe 'validations' do
    subject { FactoryBot.build(:body_part_condition) }

    it { is_expected.to validate_presence_of(:part_name) }
    it { is_expected.to validate_presence_of(:condition) }
    it { should belong_to(:vehicle_condition) }

    it 'validates uniqueness of part_name scoped to vehicle_condition_id' do
      vehicle_condition = create(:vehicle_condition)
      create(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :front_bumper)

      duplicate = build(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :front_bumper)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:part_name]).to include('has already been taken')
    end

    context 'uniqueness validation' do
      let(:vehicle_condition) { create(:vehicle_condition) }
      let!(:existing_body_part) { create(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :front_bumper, condition: :okay) }

      it 'prevents duplicate part names for the same vehicle condition' do
        duplicate_body_part = build(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :front_bumper, condition: :scratch)
        expect(duplicate_body_part).not_to be_valid
        expect(duplicate_body_part.errors[:part_name]).to include('has already been taken')
      end

      it 'allows same part name for different vehicle conditions' do
        other_vehicle_condition = create(:vehicle_condition)
        other_body_part = build(:body_part_condition, vehicle_condition: other_vehicle_condition, part_name: :front_bumper, condition: :damaged)
        expect(other_body_part).to be_valid
      end

      it 'allows different part names for the same vehicle condition' do
        different_body_part = build(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :bonnet, condition: :chip)
        expect(different_body_part).to be_valid
      end
    end
  end

  describe 'photo attachments' do
    let(:body_part_condition) { FactoryBot.build(:body_part_condition) }

    it 'is valid with a valid image file' do
      body_part_condition.media_files.attach(
        io: StringIO.new('image content'),
        filename: 'photo.jpg',
        content_type: 'image/jpeg'
      )
      expect(body_part_condition).to be_valid
    end

    it 'allows multiple images' do
      3.times do |i|
        body_part_condition.media_files.attach(
          io: StringIO.new('image content'),
          filename: "photo_#{i}.jpg",
          content_type: 'image/jpeg'
        )
      end
      expect(body_part_condition).to be_valid
    end

    it 'is invalid with a non-image file' do
      body_part_condition.media_files.attach(
        io: StringIO.new('not an image'),
        filename: 'file.txt',
        content_type: 'text/plain'
      )
      expect(body_part_condition).not_to be_valid # No custom validation on content_type in model
    end
  end

  describe 'new part names' do
    let(:vehicle_condition) { create(:vehicle_condition) }

    it 'allows roof as a valid part name' do
      body_part = build(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :roof)
      expect(body_part).to be_valid
      expect(body_part.roof?).to be true
    end

    it 'allows rear_fender_skirt as a valid part name' do
      body_part = build(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :rear_fender_skirt)
      expect(body_part).to be_valid
      expect(body_part.rear_fender_skirt?).to be true
    end

    it 'allows rear_bumper as a valid part name' do
      body_part = build(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :rear_bumper)
      expect(body_part).to be_valid
      expect(body_part.rear_bumper?).to be true
    end

    it 'ensures new part names have correct enum values' do
      expect(described_class.part_names['roof']).to eq(34)
      expect(described_class.part_names['rear_fender_skirt']).to eq(35)
      expect(described_class.part_names['rear_bumper']).to eq(36)
    end
  end
end
