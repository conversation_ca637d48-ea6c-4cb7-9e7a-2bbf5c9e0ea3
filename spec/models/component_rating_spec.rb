require 'rails_helper'

RSpec.describe Vehicle::ComponentRating, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:vehicle_condition) }
    it { is_expected.to have_many_attached(:media_files) }
  end

  describe 'validations' do
    subject { FactoryBot.build(:component_rating) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:rating) }
    it { is_expected.to validate_numericality_of(:rating).only_integer.is_greater_than_or_equal_to(1).is_less_than_or_equal_to(5) }

    it 'validates uniqueness of name scoped to vehicle_condition_id' do
      vehicle_condition = create(:vehicle_condition)
      create(:component_rating, vehicle_condition: vehicle_condition, name: :front_wheels)

      duplicate = build(:component_rating, vehicle_condition: vehicle_condition, name: :front_wheels)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:name]).to include('has already been taken')
    end

    context 'uniqueness validation' do
      let(:vehicle_condition) { create(:vehicle_condition) }
      let!(:existing_component) { create(:component_rating, vehicle_condition: vehicle_condition, name: :front_wheels, rating: 3) }

      it 'prevents duplicate component names for the same vehicle condition' do
        duplicate_component = build(:component_rating, vehicle_condition: vehicle_condition, name: :front_wheels, rating: 4)
        expect(duplicate_component).not_to be_valid
        expect(duplicate_component.errors[:name]).to include('has already been taken')
      end

      it 'allows same component name for different vehicle conditions' do
        other_vehicle_condition = create(:vehicle_condition)
        other_component = build(:component_rating, vehicle_condition: other_vehicle_condition, name: :front_wheels, rating: 5)
        expect(other_component).to be_valid
      end

      it 'allows different component names for the same vehicle condition' do
        different_component = build(:component_rating, vehicle_condition: vehicle_condition, name: :rear_wheels, rating: 4)
        expect(different_component).to be_valid
      end
    end
  end

  describe 'enum' do
    it 'defines the correct enum values for name' do
      expect(described_class.names.keys).to match_array(%w[
        front_wheels rear_wheels panel_work paint_work interior windscreen mechanical
      ])
    end

    it 'provides enum helper methods' do
      rating = FactoryBot.build(:component_rating, name: :front_wheels)
      expect(rating.front_wheels?).to be true
    end
  end

  describe 'photo attachments' do
    let(:component_rating) { FactoryBot.build(:component_rating) }

    it 'can have media_files attached' do
      component_rating.media_files.attach(
        io: StringIO.new('image content'),
        filename: 'test_photo.jpg',
        content_type: 'image/jpeg'
      )
      expect(component_rating.media_files).to be_attached
    end

    it 'can have multiple media_files attached' do
      3.times do |i|
        component_rating.media_files.attach(
          io: StringIO.new('image content'),
          filename: "photo_#{i}.jpg",
          content_type: 'image/jpeg'
        )
      end
      expect(component_rating.media_files.count).to eq(3)
    end
  end
end
