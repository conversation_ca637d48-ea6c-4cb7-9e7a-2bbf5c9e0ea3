require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::Vehicles", type: :request do
  include_context "dealership_api_shared_context"

  path "/api/v1/dealerships/{dealership_uuid}/vehicles" do
    post "Create a new vehicle with photo uploads" do
      tags "Vehicles"
      produces "application/json"
      consumes "multipart/form-data"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true
      parameter name: "Device-ID", in: :header, type: :string, required: true
      parameter name: :dealership_uuid, in: :path, type: :string, required: true

      parameter name: :vehicle, in: :formData, schema: {
        type: :object,
        properties: {
          brand_uuid: { type: :string, description: "Brand UUID" },
          make: { type: :string, description: "Vehicle make" },
          model: { type: :string, description: "Vehicle model" },
          build_year: { type: :integer, description: "Vehicle build year" },
          color: { type: :string, description: "Vehicle color" },
          vin: { type: :string, description: "Vehicle VIN" },
          stock_number: { type: :string, description: "Stock number" },
          rego: { type: :string, description: "Registration number" },
          status: { type: :string, enum: [ "available", "in_use", "out_of_service", "sold", "enquiry" ] },
          vehicle_type: { type: :string, enum: [ "new_vehicle", "demo", "old" ] },
          rego_expiry: { type: [ :string, :null ], format: :date, description: "Registration expiry date" },
          is_trade_plate_used: { type: :boolean, description: "Flag indicating if trade plate is used" },
          available_for_drive: { type: :boolean, description: "Flag indicating if vehicle is available for drive" },
          last_known_odometer_km: { type: [ :integer, :null ], description: "Last known odometer reading in kilometers" },
          last_known_fuel_gauge_level: { type: [ :integer, :null ], description: "Last known fuel gauge level" },
          media_files: { type: :array, items: { type: :string, format: :binary, description: "Vehicle photo (PNG/JPEG only, max 5MB, max 5 files)" } }
        },
        required: [ 'make', 'model', 'build_year' ]
      }

      response "201", "Vehicle created with single photo" do
        let(:dealership_uuid) { dealership.uuid }
        let(:photo1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
        let(:vehicle) do
          {
            make: "Honda",
            model: "Civic",
            build_year: 2023,
            color: "Blue",
            vehicle_type: "new_vehicle",
            vin: "1HGBH41JXMN109186",
            stock_number: "TOY001",
            rego: "ABC123",
            rego_expiry: "2026-12-31",
            status: "available",
            available_for_drive: true,
            brand_uuid: toyota.uuid,
            is_trade_plate_used: false,
            last_known_odometer_km: 10000,
            last_known_fuel_gauge_level: 50,
            last_system_inspection_timestamp: 1.month.ago,
            display_name: "2023 Honda Civic",
            media_files: [ photo1 ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Vehicle created successfully")
          expect(data.dig("data", "vehicle", "make")).to eq("Honda")
          expect(data.dig("data", "vehicle", "brand", "name")).to eq("Toyota")
          expect(data.dig("data", "vehicle", "media_files_count")).to eq(1)
        end
      end

      response "201", "Vehicle created without media_files" do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2023, status: "available" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Vehicle created successfully")
          expect(data.dig("data", "vehicle", "make")).to eq("Toyota")
          expect(data.dig("data", "vehicle", "media_files_count")).to eq(0)
          expect(data.dig("data", "vehicle", "media_files")).to eq(nil)
        end
      end

      response "201", "Vehicle created successfully with media_files" do
        let(:dealership_uuid) { dealership.uuid }
        let(:photo1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
        let(:photo2) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/png") }
        let(:vehicle) do
          {
            make: "Toyota",
            model: "Camry",
            build_year: 2023,
            color: "Blue",
            vehicle_type: "new_vehicle",
            vin: "1HGBH41JXMN109186",
            stock_number: "TOY001",
            rego: "ABC123",
            rego_expiry: "2026-12-31",
            status: "available",
            available_for_drive: true,
            brand_uuid: toyota.uuid,
            is_trade_plate_used: false,
            last_known_odometer_km: 10000,
            last_known_fuel_gauge_level: 50,
            last_system_inspection_timestamp: 1.month.ago,
            display_name: "2023 Toyota Camry",
            media_files: [ photo1, photo2 ]
          }
        end

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: "Vehicle created successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     vehicle: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid },
                         make: { type: :string, example: "Toyota" },
                         model: { type: :string, example: "Camry" },
                         build_year: { type: :integer, example: 2023 },
                         color: { type: :string, example: "Blue" },
                         vin: { type: :string, example: "1HGBH41JXMN109186" },
                         stock_number: { type: :string, example: "TOY001" },
                         rego: { type: :string, example: "ABC123" },
                         status: { type: :string, example: "available" },
                         vehicle_type: { type: :string, example: "new_vehicle" },
                         media_files_count: { type: :integer, example: 2 },
                         media_files: {
                           type: :array,
                           items: {
                             type: :object,
                             properties: {
                               id: { type: :integer },
                               url: { type: :string, format: :uri }
                             }
                           }
                         },
                         brand: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, format: :uuid },
                             name: { type: :string, example: "Toyota" }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Vehicle created successfully")
          expect(data.dig("data", "vehicle", "make")).to eq("Toyota")
          expect(data.dig("data", "vehicle", "media_files_count")).to eq(2)
          expect(data.dig("data", "vehicle", "media_files")).to be_an(Array)
          expect(data.dig("data", "vehicle", "media_files").length).to eq(2)
        end
      end

      response "422", "Validation error with media_files" do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) do
          {
            make: "Toyota",
            model: "Camry",
            build_year: 2023,
            media_files: [ fixture_file_upload(Rails.root.join("spec/fixtures/files/invalid.txt"), "text/plain") ]
          }
        end

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "media_files has an invalid content type" }
                   }
                 }
               }

        run_test!
      end

      response "422", "Too many media_files error" do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) do
          {
            make: "BMW",
            model: "X5",
            build_year: 2023,
            media_files: [
              fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png"),
              fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/jpeg"),
              fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png"),
              fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/jpeg"),
              fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png"),
              fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/jpeg")
            ]
          }
        end

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "media_files exceeds maximum limit of 5 files" }
                   }
                 }
               }

        run_test!
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2023, status: "available" } }

        run_test!
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "non-existent-uuid" }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2023, status: "available" } }

        run_test!
      end

      response "404", "Brand not found" do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) { { make: "Toyota", model: "Camry", build_year: 2023, status: "available", brand_uuid: "non-existent-uuid" } }

        run_test!
      end

      response "422", "Validation error" do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) { { color: "Red" } }

        run_test!
      end

    response "400", "Bad request" do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle) { { make: "BMW", model: "X5", build_year: 2023, vehicle_type: "invalid_type" } }

        run_test!
      end
    end
  end

  describe "POST /api/v1/dealerships/:dealership_uuid/vehicles" do
    subject { post "/api/v1/dealerships/#{dealership.uuid}/vehicles", params: params, headers: headers, as: :json }

    context "with valid vehicle data" do
      let(:params) do
        {
          vehicle:
          {
            vin: "12345678901234567",
            stock_number: "H001",
            rego: "GHI789",
            make: "Honda",
            model: "Civic",
            color: "White",
            build_year: 2022,
            rego_expiry: "2024-12-31",
            is_trade_plate_used: false,
            vehicle_type: "demo",
            available_for_drive: true,
            last_known_odometer_km: 12345,
            last_known_fuel_gauge_level: 50
          }
        }
      end

      it "creates a new vehicle successfully" do
        expect { subject }.to change(DealershipVehicle, :count).by(1)

        expect(response).to have_http_status(:created)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Vehicle created successfully")
        vehicle_data = json.dig("data", "vehicle")

        expect(vehicle_data["make"]).to eq("Honda")
        expect(vehicle_data["model"]).to eq("Civic")
        expect(vehicle_data["build_year"]).to eq(2022)
        expect(vehicle_data["color"]).to eq("White")
        expect(vehicle_data["vin"]).to eq("12345678901234567")
        expect(vehicle_data["stock_number"]).to eq("H001")
        expect(vehicle_data["rego"]).to eq("GHI789")
        expect(vehicle_data["rego_expiry"]).to eq("2024-12-31")
        expect(vehicle_data["is_trade_plate_used"]).to eq(false)
        expect(vehicle_data["vehicle_type"]).to eq("demo")
        expect(vehicle_data["available_for_drive"]).to eq(true)
        expect(vehicle_data["last_known_odometer_km"]).to eq(12345)
        expect(vehicle_data["last_known_fuel_gauge_level"]).to eq(50)
        expect(vehicle_data["uuid"]).to be_present
      end
    end

    context "with minimal required data" do
      let(:params) { { vehicle: { make: "Ford", model: "Focus", build_year: 2021 } } }

      it "creates vehicle with defaults" do
        expect { subject }.to change(DealershipVehicle, :count).by(1)

        expect(response).to have_http_status(:created)
        json = response.parsed_body

        vehicle_data = json.dig("data", "vehicle")
        expect(vehicle_data["make"]).to eq("Ford")
        expect(vehicle_data["model"]).to eq("Focus")
        expect(vehicle_data["build_year"]).to eq(2021)
        expect(vehicle_data["status"]).to eq("available")
      end
    end

    context "with invalid data" do
      context "missing make" do
        let(:params) { { vehicle: { model: "Civic", build_year: 2022 } } }

        it "returns validation error" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Make can't be blank")
        end
      end

      context "missing model" do
        let(:params) { { vehicle: { make: "Honda", build_year: 2022 } } }

        it "returns validation error" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Model can't be blank")
        end
      end

      context "missing year" do
        let(:params) { { vehicle: { make: "Honda", model: "Civic" } } }

        it "returns validation error" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Build year can't be blank")
        end
      end

      context "invalid status" do
        let(:params) { { vehicle: { make: "BMW", model: "X5", build_year: 2023, status: "invalid_status" } } }

        it "returns validation error" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:bad_request)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("is not a valid status")
        end
      end

      context "invalid vehicle type" do
        let(:params) { { vehicle: { make: "BMW", model: "X5", build_year: 2023, vehicle_type: "invalid_type" } } }

        it "returns validation error" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:bad_request)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("is not a valid vehicle_type")
        end
      end

      context "invalid year" do
        let(:params) { { vehicle: { make: "BMW", model: "X5", build_year: 1800 }  } }

        it "returns validation error" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Build year must be greater")
        end
      end
    end

    context "with invalid dealership" do
      subject { post "/api/v1/dealerships/non-existent-uuid/vehicles", params: params, headers: headers, as: :json }
      let(:params) { { make: "Toyota", model: "Camry", build_year: 2023 } }

      it "returns not found error" do
        expect { subject }.not_to change(DealershipVehicle, :count)

        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { { "Content-Type" => "application/json", "Host" => "localhost" } }
      let(:params) { { make: "Toyota", model: "Camry", build_year: 2023 } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "without dealership access" do
      let(:other_dealership) { create(:dealership) }
      subject { post "/api/v1/dealerships/#{other_dealership.uuid}/vehicles", params: params, headers: headers, as: :json }
      let(:params) { { make: "Toyota", model: "Camry", build_year: 2023 } }

      it "returns not found" do
        expect { subject }.not_to change(DealershipVehicle, :count)

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "POST /api/v1/dealerships/:dealership_uuid/vehicles with photo uploads" do
    let(:multipart_headers) do
      {
        "Authorization" => "Bearer #{valid_token}",
        "Device-ID" => device_registration.device_id,
        "Host" => "localhost"
      }
    end

    subject { post "/api/v1/dealerships/#{dealership.uuid}/vehicles", params: params, headers: multipart_headers }

    context "with multiple photo uploads" do
      let(:photo1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
      let(:photo2) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/png") }
      let(:photo3) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
      let(:params) do
        {
          vehicle:
          {
            make: "Audi",
            model: "A4",
            build_year: 2024,
            color: "Blue",
            brand: "Audi",
            vin: "WAUZZZ8K2DA123456",
            stock_number: "AUD001",
            status: "available",
            vehicle_type: "new_vehicle",
            media_files: [ photo1, photo2, photo3 ]
          }
      }
      end

      it "creates vehicle with multiple media_files" do
        expect { subject }.to change(DealershipVehicle, :count).by(1)

        expect(response).to have_http_status(:created)
        json = response.parsed_body

        vehicle = DealershipVehicle.last
        expect(vehicle.media_files.count).to eq(3)
        expect(vehicle.media_files.attached?).to be true

        # Check photo content types
        content_types = vehicle.media_files.map(&:content_type)
        expect(content_types).to include("image/jpeg", "image/png")

        vehicle_data = json.dig("data", "vehicle")
        expect(vehicle_data["make"]).to eq("Audi")
        expect(vehicle_data["model"]).to eq("A4")
        expect(vehicle_data["media_files_count"]).to eq(3)
        expect(vehicle_data["media_files"]).to be_an(Array)
        expect(vehicle_data["media_files"].length).to eq(3)

        # Verify all URLs are valid photo URLs
        vehicle_data["media_files"].each do |media_file|
          expect(media_file).to have_key("id")
          expect(media_file).to have_key("url")
          expect(media_file.dig("url")).to match(/^https?:\/\//)
        end
      end
    end

    context "with photo validation errors" do
      context "exceeding maximum photo limit" do
        let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
        let(:params) do
          {
            vehicle:
            {
              make: "Ferrari",
              model: "F8",
              build_year: 2024,
              media_files: [ photo, photo, photo, photo, photo, photo ]
            }
          }
        end

        it "returns validation error for too many media_files" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Media files")
        end
      end

      context "with invalid file type" do
        let(:invalid_file) { fixture_file_upload(Rails.root.join("spec/fixtures/files/test_document.txt"), "text/plain") }
        let(:params) do
          {
            vehicle:
            {
              make: "Lamborghini",
              model: "Huracan",
              build_year: 2024,
              media_files: [ invalid_file ]
            }
          }
        end

        before do
          # Create a text file for testing invalid file type
          Rails.root.join("spec/fixtures/files/test_document.txt").write("This is a text file")
        end

        after do
          # Clean up the test file
          Rails.root.join("spec/fixtures/files/test_document.txt").delete if Rails.root.join("spec/fixtures/files/test_document.txt").exist?
        end

        it "returns validation error for invalid file type" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Media files")
        end
      end

      context "with single photo upload" do
        let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
        let(:params) do
          {
            vehicle:
            {
              make: "Tesla",
              model: "Model 3",
              build_year: 2024,
              color: "White",
              media_files: [ photo ]
            }
          }
        end

        it "creates vehicle with single photo" do
          expect { subject }.to change(DealershipVehicle, :count).by(1)

          expect(response).to have_http_status(:created)
          json = response.parsed_body

          vehicle = DealershipVehicle.last
          expect(vehicle.media_files.count).to eq(1)
          expect(vehicle.media_files.attached?).to be true
          expect(vehicle.media_files.first.content_type).to eq("image/png")

          vehicle_data = json.dig("data", "vehicle")
          expect(vehicle_data["make"]).to eq("Tesla")
          expect(vehicle_data["model"]).to eq("Model 3")
          expect(vehicle_data["media_files_count"]).to eq(1)
          expect(vehicle_data["media_files"]).to be_an(Array)
          expect(vehicle_data["media_files"].length).to eq(1)
        end
      end

      context "with no media_files" do
        let(:params) do
          {
            vehicle:
            {
              make: "Nissan",
              model: "Altima",
              build_year: 2024,
              color: "Silver"
            }
          }
        end

        it "creates vehicle without media_files" do
          expect { subject }.to change(DealershipVehicle, :count).by(1)

          expect(response).to have_http_status(:created)
          json = response.parsed_body

          vehicle = DealershipVehicle.last
          expect(vehicle.media_files.count).to eq(0)
          expect(vehicle.media_files.attached?).to be false

          vehicle_data = json.dig("data", "vehicle")
          expect(vehicle_data["make"]).to eq("Nissan")
          expect(vehicle_data["model"]).to eq("Altima")
          expect(vehicle_data["media_files_count"]).to eq(0)
          expect(vehicle_data["media_files"]).to eq(nil)
        end
      end

      context "with mixed valid and invalid media_files" do
        let(:valid_photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/jpeg") }
        let(:invalid_photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/invalid.txt"), "text/plain") }
        let(:params) do
          {
            vehicle:
            {
              make: "BMW",
              model: "X3",
              build_year: 2024,
              media_files: [ valid_photo, invalid_photo ]
            }
          }
        end

        it "returns validation error for mixed photo types" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Media files")
        end
      end

      context "with large file size" do
        let(:params) do
          {
            vehicle:
            {
              make: "Mercedes",
              model: "C-Class",
              build_year: 2024,
              media_files: [ fixture_file_upload(Rails.root.join("spec/fixtures/files/large_car_image.png"), "image/png") ]
            }
          }
        end

        it "returns validation error for large file" do
          expect { subject }.not_to change(DealershipVehicle, :count)

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Media files")
        end
      end

      context "with different image formats" do
        let(:jpg_photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/jpeg") }
        let(:png_photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/png") }
        let(:params) do
          {
            vehicle:
            {
              make: "Volkswagen",
              model: "Golf",
              build_year: 2024,
              media_files: [ jpg_photo, png_photo ]
            }
          }
        end

        it "creates vehicle with different image formats" do
          expect { subject }.to change(DealershipVehicle, :count).by(1)

          expect(response).to have_http_status(:created)
          json = response.parsed_body

          vehicle = DealershipVehicle.last
          expect(vehicle.media_files.count).to eq(2)
          content_types = vehicle.media_files.map(&:content_type)
          expect(content_types).to include("image/jpeg", "image/png")

          vehicle_data = json.dig("data", "vehicle")
          expect(vehicle_data["media_files_count"]).to eq(2)
          expect(vehicle_data["media_files"].length).to eq(2)
        end
      end
    end
  end
end
