# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT/PATCH /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) do
    create(:customer_vehicle,
      appraisal: appraisal,
      customer: appraisal.customer,
      dealership: appraisal.dealership,
      make: "Toyota",
      model: "Camry",
      build_year: 2020,
      vin: "ORIGINAL_VIN",
      exterior_color: "White"
    )
  end

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle" do
    put "Update vehicle for appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :vehicle, in: :formData, schema: {
        type: :object,
        properties: {
          make: { type: :string, description: "Vehicle make", example: "Honda" },
          model: { type: :string, description: "Vehicle model", example: "Civic" },
          build_year: { type: :integer, description: "Vehicle build year", example: 2021 },
          vin: { type: :string, description: "Vehicle identification number", example: "1HGBH41JXMN109186" },
          rego: { type: :string, description: "Registration number", example: "ABC123" },
          registration_state: { type: :string, description: "Registration state", example: "NSW" },
          registration_expiry: { type: :string, format: :date, description: "Registration expiry date" },
          build_month: { type: :integer, description: "Vehicle build month (1-12)" },
          compliance_year: { type: :integer, description: "Compliance year" },
          compliance_month: { type: :integer, description: "Compliance month (1-12)" },
          exterior_color: { type: :string, description: "Exterior color", example: "Red" },
          interior_color: { type: :string, description: "Interior color", example: "Black" },
          engine_kilowatts: { type: :integer, description: "Engine power in kilowatts", example: 150 },
          engine_number: { type: :string, description: "Engine number" },
          engine_size: { type: :string, description: "Engine size", example: "2.0L" },
          wheel_size: { type: :string, description: "Wheel size", example: "17 inch" },
          wheel_size_front: { type: :integer, description: "Front wheel size in inches", example: 17 },
          wheel_size_rear: { type: :integer, description: "Rear wheel size in inches", example: 17 },
          odometer_reading: { type: :integer, description: "Odometer reading", example: 60000 },
          odometer_date: { type: :string, format: :date, description: "Odometer reading date" },
          redbook_code: { type: :string, description: "Redbook code" },
          seat_type: { type: :string, enum: [ "leather", "cloth", "mixed" ], description: "Seat type" },
          fuel_type: { type: :string, enum: [ "petrol", "diesel", "electric", "hybrid", "plugin_hybrid", "lpg", "other" ], description: "Fuel type" },
          driving_wheels: { type: :string, enum: [ "fwd", "rwd", "awd", "four_wd" ], description: "Driving wheels" },
          spare_wheel_type: { type: :string, enum: [ "full_size", "space_saver", "run_flat", "repair_kit", "no_spare_wheel" ], description: "Spare wheel type" },
          transmission: { type: :string, enum: [ "manual", "automatic", "cvt", "semi_automatic", "dual_clutch" ], description: "Transmission type" },
          body_type: { type: :string, enum: [ "sedan", "hatchback", "wagon", "suv", "coupe", "convertible", "ute", "van", "truck", "unknown_body" ], description: "Body type" },
          number_of_doors: { type: :integer, description: "Number of doors", example: 4 },
          number_of_seats: { type: :integer, description: "Number of seats", example: 5 },
          is_vehicle_present: { type: :boolean, description: "Is vehicle present for inspection" },
          main_photo: { type: :string, format: :binary, description: "Main vehicle photo" },
          odometer_reading_photo: { type: :string, format: :binary, description: "Odometer reading photo" },
          media_files: { type: :array, items: { type: :string, format: :binary }, description: "Multiple vehicle media_files (max 5)", maxItems: 5 }
        }
      }


      response "200", "fields and brand can be cleared" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Vehicle updated successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string },
                         vehicle: {
                           type: :object,
                           properties: {
                             seat_type: { type: :string, nullable: true },
                             fuel_type: { type: :string, nullable: true },
                             brand: { type: :object, nullable: true }
                           }
                         }
                       }
                     }
                   }
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal_with_enums.uuid }
        let!(:appraisal_with_enums) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let!(:vehicle_with_enums) do
          create(:customer_vehicle,
            appraisal: appraisal_with_enums,
            customer: appraisal_with_enums.customer,
            dealership: appraisal_with_enums.dealership,
            seat_type: :leather,
            fuel_type: :petrol,
            driving_wheels: :fwd,
            spare_wheel_type: :full_size,
            transmission: :automatic,
            body_type: :sedan,
            number_of_doors: 4,
            number_of_seats: 5,
            engine_kilowatts: 150,
            odometer_reading: 50000
          )
        end

        context "when clearing fields" do
          let(:vehicle) do
            {
              seat_type: "",
              fuel_type: nil,
              driving_wheels: "",
              spare_wheel_type: nil,
              transmission: "",
              body_type: nil,
              number_of_doors: "",
              number_of_seats: "",
              engine_kilowatts: "",
              odometer_reading: ""
            }
          end

          run_test! do |response|
            json = JSON.parse(response.body)
            expect(json.dig("status", "code")).to eq(200)
            expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
            expect(json.dig("data", "appraisal", "vehicle", "seat_type")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "fuel_type")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "driving_wheels")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "spare_wheel_type")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "transmission")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "body_type")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "number_of_doors")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "number_of_seats")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "engine_kilowatts")).to be_nil
            expect(json.dig("data", "appraisal", "vehicle", "odometer_reading")).to be_nil
          end
        end

        context "when clearing brand_uuid" do
          let(:vehicle) { { brand_uuid: "" } }

          run_test! do |response|
            json = JSON.parse(response.body)
            expect(json.dig("status", "code")).to eq(200)
            expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
            expect(json.dig("data", "appraisal", "vehicle", "brand")).to be_nil
          end
        end
      end

      response "200", "Vehicle updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Vehicle updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             email: { type: :string, example: '<EMAIL>' },
                             phone_number: { type: :string, example: '+61400000000' },
                             full_name: { type: :string, example: 'John Doe' }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Jane Smith' },
                             phone: { type: :string, example: '+61400000001' },
                             job_title: { type: :string, nullable: true, example: 'Sales Representative' }
                           }
                         },
                         created_by: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Bob' },
                             last_name: { type: :string, example: 'Johnson' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Bob Johnson' },
                             phone: { type: :string, example: '+61400000002' },
                             job_title: { type: :string, nullable: true, example: 'Sales Manager' }
                           }
                         },
                         updated_by: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Alice' },
                             last_name: { type: :string, example: 'Brown' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Alice Brown' },
                             phone: { type: :string, example: '+61400000003' },
                             job_title: { type: :string, nullable: true, example: 'Sales Representative' }
                           }
                         },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Honda' },
                             model: { type: :string, example: 'Civic' },
                             vin: { type: :string, example: '1HGBH41JXMN109186' },
                             rego: { type: :string, example: 'ABC123' },
                             registration_expiry: { type: :string, format: :date, nullable: true },
                             registration_state: { type: :string, example: 'NSW' },
                             build_year: { type: :integer, example: 2021 },
                             build_month: { type: :integer, nullable: true },
                             compliance_year: { type: :integer, nullable: true },
                             compliance_month: { type: :integer, nullable: true },
                             exterior_color: { type: :string, example: 'Red' },
                             interior_color: { type: :string, example: 'Black' },
                             seat_type: { type: :string, enum: [ 'leather', 'cloth', 'mixed' ], example: 'leather' },
                             fuel_type: { type: :string, enum: [ 'petrol', 'diesel', 'electric', 'hybrid', 'plugin_hybrid', 'lpg', 'other' ], example: 'petrol' },
                             driving_wheels: { type: :string, enum: [ 'fwd', 'rwd', 'awd', 'four_wd' ], example: 'fwd' },
                             spare_wheel_type: { type: :string, enum: [ 'full_size', 'space_saver', 'run_flat', 'repair_kit', 'no_spare_wheel' ], example: 'full_size' },
                             transmission: { type: :string, enum: [ 'manual', 'automatic', 'cvt', 'semi_automatic', 'dual_clutch' ], example: 'automatic' },
                             body_type: { type: :string, enum: [ 'sedan', 'hatchback', 'wagon', 'suv', 'coupe', 'convertible', 'ute', 'van', 'truck', 'unknown_body' ], example: 'sedan' },
                             number_of_doors: { type: :integer, example: 4 },
                             number_of_seats: { type: :integer, example: 5 },
                             engine_kilowatts: { type: :integer, nullable: true, example: 150 },
                             engine_number: { type: :string, nullable: true },
                             wheel_size_front: { type: :integer, nullable: true, example: 17 },
                             wheel_size_rear: { type: :integer, nullable: true, example: 17 },
                             odometer_reading: { type: :integer, nullable: true, example: 60000 },
                             odometer_date: { type: :string, format: :date, nullable: true },
                             redbook_code: { type: :string, nullable: true },
                             is_vehicle_present: { type: :boolean, example: true },
                             created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' },
                             updated_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' },
                             main_photo_url: { type: :string, nullable: true, example: '/rails/active_storage/blobs/redirect/abc123/main_photo.jpg' },
                             odometer_reading_photo_url: { type: :string, nullable: true, example: '/rails/active_storage/blobs/redirect/def456/odometer_photo.jpg' },
                             media_files: { type: :array, items: { type: :object, properties: { id: { type: :integer }, url: { type: :string } } }, nullable: true, example: [ { id: 1, url: '/rails/active_storage/blobs/redirect/abc123/photo1.jpg' } ] },
                             customer_uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             appraisal_uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             brand_uuid: { type: :string, format: :uuid, nullable: true }
                           }
                         },
                         created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' },
                         updated_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00.000Z' }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) do
          {
            make: "Honda",
            model: "Civic",
            build_year: 2021,
            vin: "1HGBH41JXMN109186",
            rego: "ABC123",
            registration_state: "NSW",
            registration_expiry: "2025-12-31",
            build_month: 6,
            compliance_year: 2021,
            compliance_month: 7,
            exterior_color: "Red",
            interior_color: "Black",
            seat_type: "leather",
            fuel_type: "petrol",
            driving_wheels: "fwd",
            spare_wheel_type: "full_size",
            transmission: "automatic",
            body_type: "sedan",
            number_of_doors: 4,
            number_of_seats: 5,
            engine_kilowatts: 150,
            engine_number: "ABC123456",
            wheel_size_front: 17,
            wheel_size_rear: 17,
            odometer_reading: 60000,
            odometer_date: "2024-01-15",
            redbook_code: "RB123",
            is_vehicle_present: true
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "status")).to eq("incomplete")
          expect(json.dig("data", "appraisal", "customer", "uuid")).to eq(customer.uuid)
          expect(json.dig("data", "appraisal", "sales_person", "uuid")).to eq(sales_person.uuid)
          expect(json.dig("data", "appraisal", "created_by", "uuid")).to eq(sales_person.uuid)
          expect(json.dig("data", "appraisal", "updated_by", "uuid")).to eq(sales_person.uuid)
          expect(json.dig("data", "appraisal", "vehicle")).to be_present
          expect(json.dig("data", "appraisal", "vehicle", "uuid")).to eq(customer_vehicle.uuid)
          expect(json.dig("data", "appraisal", "vehicle", "make")).to eq("Honda")
          expect(json.dig("data", "appraisal", "vehicle", "model")).to eq("Civic")
          expect(json.dig("data", "appraisal", "vehicle", "build_year")).to eq(2021)
          expect(json.dig("data", "appraisal", "vehicle", "vin")).to eq("1HGBH41JXMN109186")
          expect(json.dig("data", "appraisal", "vehicle", "rego")).to eq("ABC123")
          expect(json.dig("data", "appraisal", "vehicle", "registration_state")).to eq("NSW")
          expect(json.dig("data", "appraisal", "vehicle", "fuel_type")).to eq("petrol")
          expect(json.dig("data", "appraisal", "vehicle", "transmission")).to eq("automatic")
          expect(json.dig("data", "appraisal", "vehicle", "body_type")).to eq("sedan")
          expect(json.dig("data", "appraisal", "vehicle", "number_of_doors")).to eq(4)
          expect(json.dig("data", "appraisal", "vehicle", "number_of_seats")).to eq(5)
          expect(json.dig("data", "appraisal", "vehicle", "odometer_reading")).to eq(60000)
          expect(json.dig("data", "appraisal", "vehicle", "is_vehicle_present")).to eq(true)
          expect(json.dig("data", "appraisal", "vehicle", "exterior_color")).to eq("Red")
          expect(json.dig("data", "appraisal", "vehicle", "interior_color")).to eq("Black")

          expect(json.dig("data", "appraisal", "created_at")).to be_present
          expect(json.dig("data", "appraisal", "updated_at")).to be_present
        end
      end

      response "200", "Partial update successful" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Vehicle updated successfully' }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: { type: :object }
                   }
                 }
               }


        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { exterior_color: "Blue" } }
        # Only updating exterior_color

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal", "vehicle", "exterior_color")).to eq("Blue")
          # Other fields should remain unchanged
          expect(json.dig("data", "appraisal", "vehicle", "make")).to eq("Toyota")
          expect(json.dig("data", "appraisal", "vehicle", "model")).to eq("Camry")
        end
      end

      response "422", "No vehicle found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { appraisal_without_vehicle.uuid }
        let(:appraisal_without_vehicle) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let(:vehicle) { { exterior_color: "Blue" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) do
          create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived)
        end
        let!(:archived_vehicle) do
          create(:customer_vehicle,
            appraisal: archived_appraisal,
            customer: archived_appraisal.customer,
            dealership: archived_appraisal.dealership
          )
        end
        let(:vehicle) { { exterior_color: "Blue" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:vehicle) { { exterior_color: "Blue" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
        end
      end

      response "404", "Dealership not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Dealership not found or you don't have access to it" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:dealership_uuid) { "non-existent-uuid" }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { exterior_color: "Blue" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Dealership not found or you don't have access to it")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { exterior_color: "Blue" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response "401", "Invalid device" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Invalid device" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:'Device-ID') { nil }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:vehicle) { { exterior_color: "Blue" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Invalid device")
        end
      end
    end
  end

  # Comprehensive Functional Tests
  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/vehicle" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/vehicle" }
    let(:test_headers) { { "Authorization" => "Bearer #{valid_token}", "Device-ID" => device_registration.device_id } }

    subject { put url, params: { vehicle: vehicle_params }, headers: test_headers, as: :json }

    context "with non-nullable field validation" do
      let!(:vehicle_with_values) do
        create(:customer_vehicle,
          appraisal: appraisal,
          customer: appraisal.customer,
          dealership: appraisal.dealership,
          number_of_doors: 4,
          number_of_seats: 5,
          engine_kilowatts: 150,
          odometer_reading: 50000
        )
      end

      context "when updating non-nullable fields to valid values" do
        let(:vehicle_params) do
          {
            number_of_doors: 2,
            number_of_seats: 4,
            engine_kilowatts: 200,
            odometer_reading: 75000
          }
        end

        it "updates successfully" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal", "vehicle", "number_of_doors")).to eq(2)
          expect(json.dig("data", "appraisal", "vehicle", "number_of_seats")).to eq(4)
          expect(json.dig("data", "appraisal", "vehicle", "engine_kilowatts")).to eq(200)
          expect(json.dig("data", "appraisal", "vehicle", "odometer_reading")).to eq(75000)
        end
      end
    end

    context "with enum field clearing" do
      let!(:vehicle_with_enums) do
        create(:customer_vehicle,
          appraisal: appraisal,
          customer: appraisal.customer,
          dealership: appraisal.dealership,
          seat_type: :leather,
          fuel_type: :petrol,
          driving_wheels: :fwd,
          spare_wheel_type: :full_size,
          transmission: :automatic,
          body_type: :sedan
        )
      end

      context "when clearing enum fields with nil" do
        let(:vehicle_params) do
          {
            seat_type: nil,
            fuel_type: nil,
            driving_wheels: nil,
            spare_wheel_type: nil,
            transmission: nil,
            body_type: nil
          }
        end

        it "clears enum fields successfully" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal", "vehicle", "seat_type")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "fuel_type")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "driving_wheels")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "spare_wheel_type")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "transmission")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "body_type")).to be_nil
        end
      end

      context "when clearing enum fields with empty string" do
        let(:vehicle_params) do
          {
            seat_type: "",
            fuel_type: "",
            driving_wheels: "",
            spare_wheel_type: "",
            transmission: "",
            body_type: ""
          }
        end

        it "clears enum fields successfully" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal", "vehicle", "seat_type")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "fuel_type")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "driving_wheels")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "spare_wheel_type")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "transmission")).to be_nil
          expect(json.dig("data", "appraisal", "vehicle", "body_type")).to be_nil
        end
      end
    end

    context "with brand_uuid clearing" do
      let!(:brand) { create(:brand, name: "Test Brand") }
      let!(:vehicle_with_brand) do
        create(:customer_vehicle,
          appraisal: appraisal,
          customer: appraisal.customer,
          dealership: appraisal.dealership,
          brand: brand
        )
      end

      context "when clearing brand_uuid with nil" do
        let(:vehicle_params) { { brand_uuid: nil } }

        it "clears brand successfully" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal", "vehicle", "brand")).to be_nil
        end
      end

      context "when clearing brand_uuid with empty string" do
        let(:vehicle_params) { { brand_uuid: "" } }

        it "clears brand successfully" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
          expect(json.dig("data", "appraisal", "vehicle", "brand")).to be_nil
        end
      end
    end

    context "when fields are initially null" do
      let!(:appraisal_without_values) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
      let!(:vehicle_without_values) do
        create(:customer_vehicle,
          appraisal: appraisal_without_values,
          customer: appraisal_without_values.customer,
          dealership: appraisal_without_values.dealership,
          number_of_doors: nil,
          number_of_seats: nil,
          engine_kilowatts: nil,
          odometer_reading: nil
        )
      end
      let(:url) { "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal_without_values.uuid}/vehicle" }

      context "when setting null fields to null again" do
        let(:vehicle_params) do
          {
            number_of_doors: nil,
            number_of_seats: "",
            engine_kilowatts: nil,
            odometer_reading: ""
          }
        end

        it "allows setting null fields to null" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle updated successfully")
        end
      end
    end
  end
end
