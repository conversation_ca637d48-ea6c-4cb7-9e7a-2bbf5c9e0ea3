# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "POST /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/award", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :complete) }
  let(:appraisal_uuid) { appraisal.uuid }
  let(:valuer) { create(:appraisal_valuer, dealership: dealership) }

  let!(:appraisal_offer) do
    create(:appraisal_offer,
           appraisal: appraisal,
           appraisal_valuer: valuer,
           offer_price: 25000.00,
           offer_notes: "Test offer",
           offer_date: Date.current,
           awarded: false)
  end

  let!(:already_awarded_offer) do
    create(:appraisal_offer,
           appraisal: appraisal,
           appraisal_valuer: valuer,
           offer_price: 23000.00,
           offer_notes: "Already awarded offer",
           offer_date: Date.current,
           awarded: true,
           awarded_at: 1.day.ago)
  end

  let(:offer_uuid) { appraisal_offer.uuid }
  let(:award_params) { { offer_uuid: offer_uuid } }

  # Regular RSpec tests
  describe 'POST /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/award' do
    subject { post "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/award", params: award_params, headers: headers }

    context 'when authenticated' do
      context 'with valid parameters' do
        it 'awards the offer successfully' do
          expect { subject }.to change { appraisal_offer.reload.awarded }.from(false).to(true)
                           .and change { appraisal_offer.reload.awarded_at }.from(nil)
                           .and change { appraisal.reload.status }.from('complete').to('awarded')
                           .and change { appraisal.reload.price }.to(25000.00)

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body['status']['code']).to eq(200)
          expect(response.parsed_body['status']['message']).to eq("Appraisal offer awarded successfully")
          expect(response.parsed_body['data']['appraisal']['status']).to eq('awarded')
          expect(response.parsed_body['data']['appraisal']['price']).to eq('25000.0')
        end

        it 'sets awarded_at to current time' do
          freeze_time do
            subject
            expect(appraisal_offer.reload.awarded_at).to be_within(1.second).of(Time.current)
          end
        end
      end

      context 'with offer_price parameter' do
        let(:award_params) { { offer_uuid: offer_uuid, offer_price: 27000.00 } }

        it 'updates the offer price and awards the offer' do
          expect { subject }.to change { appraisal_offer.reload.offer_price }.from(25000.00).to(27000.00)
                           .and change { appraisal_offer.reload.awarded }.from(false).to(true)
                           .and change { appraisal.reload.price }.to(27000.00)

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body['data']['appraisal']['price']).to eq('27000.0')
        end
      end

      context 'with invalid parameters' do
        context 'when offer_uuid is missing' do
          let(:award_params) { {} }

          it 'returns validation error' do
            subject
            expect(response).to have_http_status(:unprocessable_content)
            expect(response.parsed_body['status']['message']).to eq("offer_uuid is required")
          end
        end

        context 'when offer_uuid is blank' do
          let(:award_params) { { offer_uuid: '' } }

          it 'returns validation error' do
            subject
            expect(response).to have_http_status(:unprocessable_content)
            expect(response.parsed_body['status']['message']).to eq("offer_uuid is required")
          end
        end

        context 'when offer_price is zero' do
          let(:award_params) { { offer_uuid: offer_uuid, offer_price: 0 } }

          it 'returns validation error' do
            subject
            expect(response).to have_http_status(:unprocessable_content)
            expect(response.parsed_body['status']['message']).to eq("offer_price must be greater than 0")
          end
        end

        context 'when offer_price is negative' do
          let(:award_params) { { offer_uuid: offer_uuid, offer_price: -1000 } }

          it 'returns validation error' do
            subject
            expect(response).to have_http_status(:unprocessable_content)
            expect(response.parsed_body['status']['message']).to eq("offer_price must be greater than 0")
          end
        end

        context 'when offer does not exist' do
          let(:award_params) { { offer_uuid: 'non-existent-uuid' } }

          it 'returns not found error' do
            subject
            expect(response).to have_http_status(:not_found)
            expect(response.parsed_body['status']['message']).to eq("Appraisal offer not found")
          end
        end

        context 'when offer is already awarded' do
          let(:award_params) { { offer_uuid: already_awarded_offer.uuid } }

          it 'returns validation error' do
            subject
            expect(response).to have_http_status(:unprocessable_content)
            expect(response.parsed_body['status']['message']).to eq("Appraisal is already awarded")
          end
        end

        context 'when appraisal is already awarded' do
          before { appraisal.update!(status: :awarded) }

          it 'returns validation error' do
            subject
            expect(response).to have_http_status(:unprocessable_content)
            expect(response.parsed_body['status']['message']).to eq("Appraisal is already awarded")
          end
        end

        context 'when offer belongs to different appraisal' do
          let(:other_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
          let!(:other_offer) do
            create(:appraisal_offer,
                   appraisal: other_appraisal,
                   appraisal_valuer: valuer,
                   offer_price: 20000.00)
          end
          let(:award_params) { { offer_uuid: other_offer.uuid } }

          it 'returns not found error' do
            subject
            expect(response).to have_http_status(:not_found)
            expect(response.parsed_body['status']['message']).to eq("Appraisal offer not found")
          end
        end
      end

      context 'when appraisal is archived' do
        before { appraisal.update!(status: :archived) }

        it 'returns validation error' do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          expect(response.parsed_body['status']['message']).to eq("Cannot modify archived appraisal")
        end
      end

      context 'when appraisal is deleted' do
        before { appraisal.update!(status: :deleted) }

        it 'returns not found error' do
          subject
          expect(response).to have_http_status(:not_found)
          expect(response.parsed_body['status']['message']).to eq("Appraisal not found or does not belong to this dealership")
        end
      end
    end

    context 'when not authenticated' do
      let(:headers) { {} }

      it 'returns unauthorized' do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger API Documentation
  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/award" do
    post "Award an appraisal offer" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]
      description "Mark a specific appraisal offer as awarded, optionally update the offer price, and update the appraisal status to awarded"

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :award_data, in: :body, schema: {
        type: :object,
        properties: {
          offer_uuid: {
            type: :string,
            description: "UUID of the appraisal offer to award",
            example: "123e4567-e89b-12d3-a456-************"
          },
          offer_price: {
            type: :number,
            description: "New offer price (optional). If provided, updates the offer price before awarding",
            example: 27000.00
          }
        },
        required: [ :offer_uuid ]
      }

      response "200", "Appraisal offer awarded successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:award_data) { { offer_uuid: appraisal_offer.uuid } }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Appraisal offer awarded successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       description: "Updated appraisal with awarded status"
                     }
                   }
                 }
               }

        run_test!
      end

      response "200", "Appraisal offer awarded with updated price" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:award_data) { { offer_uuid: appraisal_offer.uuid, offer_price: 27000.00 } }

        run_test!
      end

      response "401", "Unauthorized" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:award_data) { { offer_uuid: appraisal_offer.uuid } }
        let(:Authorization) { nil }

        run_test!
      end

      response "404", "Appraisal not found" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:award_data) { { offer_uuid: appraisal_offer.uuid } }

        run_test!
      end

      response "404", "Appraisal offer not found" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:award_data) { { offer_uuid: "non-existent-offer-uuid" } }

        run_test!
      end

      response "422", "Validation error" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:award_data) { {} }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "offer_uuid is required" }
                   }
                 }
               }

        run_test!
      end

      response "422", "Offer already awarded" do
        let(:dealership_uuid) { dealership.uuid }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:award_data) { { offer_uuid: already_awarded_offer.uuid } }

        run_test!
      end
    end
  end
end
