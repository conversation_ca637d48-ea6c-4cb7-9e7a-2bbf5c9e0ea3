FactoryBot.define do
  factory :component_rating, class: 'Vehicle::ComponentRating' do
    name { Vehicle::ComponentRating::ALLOWED_NAMES.sample }
    rating { rand(1..5) }
    association :vehicle_condition, factory: :vehicle_condition

    trait :with_media_files do
      after(:build) do |component_rating|
        2.times do |i|
          component_rating.media_files.attach(
            io: StringIO.new('image content'),
            filename: "component_photo_#{i}.jpg",
            content_type: 'image/jpeg'
          )
        end
      end
    end
  end
end
