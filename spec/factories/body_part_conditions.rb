FactoryBot.define do
  factory :body_part_condition, class: 'Vehicle::BodyPartCondition' do
    association :vehicle_condition, factory: :vehicle_condition
    part_name { :front_bumper }
    condition { :okay }
    description { "No visible damage" }

    trait :with_media_files do
      after(:build) do |body_part_condition|
        body_part_condition.media_files.attach(
          io: StringIO.new("image content"),
          filename: "photo.jpg",
          content_type: "image/jpeg"
        )
      end
    end

    # Traits for new part names
    trait :roof do
      part_name { :roof }
    end

    trait :rear_fender_skirt do
      part_name { :rear_fender_skirt }
    end

    trait :rear_bumper do
      part_name { :rear_bumper }
    end
  end
end
