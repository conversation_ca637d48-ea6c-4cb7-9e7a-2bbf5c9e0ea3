{"info": {"_postman_id": "22710-be5f9b8e-b758-4253-9619-93aed4db9513", "name": "DealerDrive", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Appraisals", "item": [{"name": "createAppraisal", "id": "22710-17b2445f-f93a-4801-811a-6749b3b719eb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customer_uuid\": \"c48ddb9c-2bc2-40d0-ae14-a38a8fd649f3\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals"]}}, "response": []}, {"name": "GetAppraisals", "id": "22710-388be7dc-08ee-41d8-a277-e079d6e852d1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals?status=incomplete", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals"], "query": [{"key": "page", "value": "2", "disabled": true}, {"key": "per_page", "value": "20", "disabled": true}, {"key": "start_date", "value": "2025-01-01", "disabled": true}, {"key": "end_date", "value": "2025-12-31", "disabled": true}, {"key": "registration_number", "value": "ABC123", "disabled": true}, {"key": "salesperson_uuid", "value": "2115de97-96e7-4619-b9e0-b07c88b70225", "disabled": true}, {"key": "customer_uuid", "value": "2c5b1ae0-8ffd-4a12-9447-a8bc591d4756", "disabled": true}, {"key": "status", "value": "incomplete"}]}}, "response": []}, {"name": "AddVehicleToAppraisal", "id": "22710-39cff77f-b0c6-4e5e-b57d-8f5d0861c19f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "vehicle[make]", "value": "Toyota"}, {"type": "text", "key": "vehicle[model]", "value": "Cam<PERSON>"}, {"type": "text", "key": "vehicle[vin]", "value": "1HGBH41JXMN109186"}, {"type": "text", "key": "vehicle[rego]", "value": "ABC123"}, {"type": "text", "key": "vehicle[registration_expiry]", "value": "2025-12-31"}, {"type": "text", "key": "vehicle[registration_state]", "value": "NSW"}, {"type": "text", "key": "vehicle[build_year]", "value": "2020"}, {"type": "text", "key": "vehicle[build_month]", "value": "6"}, {"type": "text", "key": "vehicle[compliance_year]", "value": "2020"}, {"type": "text", "key": "vehicle[compliance_month]", "value": "5"}, {"type": "text", "key": "vehicle[exterior_color]", "value": "White"}, {"type": "text", "key": "vehicle[interior_color]", "value": "Black"}, {"type": "text", "key": "vehicle[seat_type]", "value": "leather"}, {"type": "text", "key": "vehicle[fuel_type]", "value": "petrol"}, {"type": "text", "key": "vehicle[driving_wheels]", "value": "fwd"}, {"type": "text", "key": "vehicle[spare_wheel_type]", "value": "full_size"}, {"type": "text", "key": "vehicle[transmission]", "value": "automatic"}, {"type": "text", "key": "vehicle[body_type]", "value": "sedan"}, {"type": "text", "key": "vehicle[number_of_doors]", "value": "4"}, {"type": "text", "key": "vehicle[number_of_seats]", "value": "5"}, {"type": "text", "key": "vehicle[engine_killowats]", "value": "150"}, {"type": "text", "key": "vehicle[engine_number]", "value": "4A-GE"}, {"type": "text", "key": "vehicle[wheel_size_front]", "value": "17"}, {"type": "text", "key": "vehicle[wheel_size_rear]", "value": "17"}, {"type": "text", "key": "vehicle[odometer_reading]", "value": "50000"}, {"type": "text", "key": "vehicle[odometer_date]", "value": "2024-01-15"}, {"type": "text", "key": "vehicle[redbook_code]", "value": "TOYOTA123"}, {"type": "file", "key": "vehicle[main_photo]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/car_1.png"}, {"type": "file", "key": "vehicle[odometer_reading_photo]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/toyota_logo.png"}, {"key": "vehicle[brand_uuid]", "value": "01dd85cf-8538-40fa-b087-d7d0228c2146", "type": "text", "uuid": "a60ea234-7115-4c26-88b4-cbbed997aa87"}, {"key": "vehicle[media_files][]", "type": "file", "uuid": "ffcac09f-1726-4956-9b8c-03395c5a55c7", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage1.jpg"}, {"key": "vehicle[media_files][]", "type": "file", "uuid": "9747dc82-f802-4868-9085-2c5ca2596263", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage2.jpg"}, {"key": "vehicle[media_files][]", "type": "file", "uuid": "a6b00c74-0980-4b0c-8c36-7039250d19fa", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage3.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/8f15f827-869c-4cdd-944c-450038fa41f3/vehicle", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "8f15f827-869c-4cdd-944c-450038fa41f3", "vehicle"]}}, "response": []}, {"name": "GetAppraisal", "id": "22710-b43d68ba-b9de-4fd2-9789-f2319f3fcc48", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/8f15f827-869c-4cdd-944c-450038fa41f3", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "8f15f827-869c-4cdd-944c-450038fa41f3"]}}, "response": []}, {"name": "DeleteAppraisal", "id": "22710-27635726-2105-4017-9a19-ca0f8095cb04", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/8f15f827-869c-4cdd-944c-450038fa41f3", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "8f15f827-869c-4cdd-944c-450038fa41f3"]}}, "response": []}, {"name": "EditAppraisal", "id": "22710-e8203ca1-29b8-4b56-82ee-cc1b107615d8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projected_arrival_date\": \"2025-09-16\",\n  \"appraisal_status\": \"lost\",\n  \"awarded_value\": 16000.50,\n  \"price\": 17000.00,\n  \"give_price\": 15500.00,\n  \"awarded_notes\": \"Final award decision 1\",\n  \"notes\": \"Updated appraisal notes 1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/6cd8e571-b845-4833-a434-bf136641c991", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "6cd8e571-b845-4833-a434-bf136641c991"]}}, "response": []}, {"name": "FavouriteApprisal", "id": "22710-c3b07c0b-8aad-443c-877a-a716f2d5edc1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/6cd8e571-b845-4833-a434-bf136641c991/favourite", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "6cd8e571-b845-4833-a434-bf136641c991", "favourite"]}}, "response": []}, {"name": "AttachAppraisalSignature", "id": "22710-038937c3-24f6-4e1f-9aaa-65878e5fec58", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "file", "key": "signature", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/toyota_logo.png"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/4e72fc50-d386-4914-a829-e7bc02eaeaf5/signature", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "4e72fc50-d386-4914-a829-e7bc02eaeaf5", "signature"]}}, "response": []}, {"name": "ReassignAppraisal", "id": "22710-145e42b3-dac5-45f5-81a3-84b66703ec55", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"sales_person_uuid\": \"cad5f319-2f4a-41f9-861f-01c8cc99802d\"            \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/a86e86ee-42cc-449b-9127-7e273cabf334/reassign", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "a86e86ee-42cc-449b-9127-7e273cabf334", "reassign"]}}, "response": []}, {"name": "VehicleCondition", "id": "22710-9314cc85-e265-448d-8970-5bf9eb49950f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"condition\": {\n    \"is_clean\": true,\n    \"is_wet\": false,\n    \"is_road_tested\": true,\n    \"has_signs_of_repair\": false,\n    \"repair_details\": \"No visible repairs1\",\n    \"additional_notes\": \"Vehicle is in excellent condition\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/a86e86ee-42cc-449b-9127-7e273cabf334/vehicle-condition", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "a86e86ee-42cc-449b-9127-7e273cabf334", "vehicle-condition"]}}, "response": []}, {"name": "BodyPartCondition", "id": "22710-411e3844-904b-4b4f-966f-be33b0f156d2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"body_part_condition\": {\n      \"part_name\": \"left_front_fender\",\n      \"condition\": \"scratch\",\n      \"description\": \"Minor scratch on lower left corner1\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/a86e86ee-42cc-449b-9127-7e273cabf334/body-part-condition", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "a86e86ee-42cc-449b-9127-7e273cabf334", "body-part-condition"]}}, "response": []}, {"name": "BodyPartConditionWithPhoto", "id": "22710-c04da759-db43-4c47-b302-d9a556fc2297", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "body_part_condition[part_name]", "value": "bonnet"}, {"type": "text", "key": "body_part_condition[condition]", "value": "scratch"}, {"type": "text", "key": "body_part_condition[description]", "value": "minor"}, {"type": "file", "key": "body_part_condition[media_files]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage1.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/a86e86ee-42cc-449b-9127-7e273cabf334/body-part-condition", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "a86e86ee-42cc-449b-9127-7e273cabf334", "body-part-condition"]}}, "response": []}, {"name": "ComponentRating", "id": "22710-b7b475cb-9ca5-4a06-8536-4468eef6bb50", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"component_rating\": {\n      \"name\": \"windscreen\",\n      \"rating\": 1\n    }\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/6cd8e571-b845-4833-a434-bf136641c991/component-rating", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "6cd8e571-b845-4833-a434-bf136641c991", "component-rating"]}}, "response": []}, {"name": "ComponentRatingWithPhoto", "id": "22710-a18084cd-078c-425a-9eae-3c51c2917110", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "component_rating[name]", "value": "windscreen"}, {"type": "text", "key": "component_rating[rating]", "value": "1"}, {"type": "file", "key": "component_rating[media_files]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage2.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/a86e86ee-42cc-449b-9127-7e273cabf334/component-rating", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "a86e86ee-42cc-449b-9127-7e273cabf334", "component-rating"]}}, "response": []}, {"name": "ReconditioningCost", "id": "22710-cf4d79c6-12f2-4bb7-9234-5619d0aa5cd0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"reconditioning_costs\": [\n        {\n            \"amount\": 1400.54,\n            \"currency\": \"AUD\",\n            \"cost_type\": \"registration\"\n        },\n        {\n            \"amount\": 1000.4,\n            \"currency\": \"AUD\",\n            \"cost_type\": \"wheels_and_tyres\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/a86e86ee-42cc-449b-9127-7e273cabf334/reconditioning-costs", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "a86e86ee-42cc-449b-9127-7e273cabf334", "reconditioning-costs"]}}, "response": []}, {"name": "ArchiveAppraisal", "id": "22710-bee9bbdd-c33e-4243-9752-920c0961ac22", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/4e8e43ae-0db7-4446-930d-0d4b3f82b5fa/archive", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "4e8e43ae-0db7-4446-930d-0d4b3f82b5fa", "archive"]}}, "response": []}, {"name": "OptionsFitted", "id": "22710-594f7869-aa5c-45c4-b60b-374a03fcf0d1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "options_fitted[has_sunroof]", "value": "false"}, {"type": "text", "key": "options_fitted[has_tinted_windows]", "value": "true"}, {"type": "text", "key": "options_fitted[has_keyless_entry]", "value": "true"}, {"type": "text", "key": "options_fitted[has_bluetooth]", "value": "true"}, {"type": "text", "key": "options_fitted[has_extended_warranty]", "value": "true"}, {"type": "text", "key": "options_fitted[extended_warranty_expiry]", "value": "2025-12-31"}, {"type": "text", "key": "options_fitted[sunroof_type]", "value": "panoramic"}, {"type": "text", "key": "options_fitted[number_of_keys]", "value": "2"}, {"type": "text", "key": "options_fitted[heated_seats]", "value": "true"}, {"type": "text", "key": "options_fitted[on_written_off_register]", "value": "no"}, {"type": "text", "key": "options_fitted[additional_options][premium_sound_system]", "value": "true"}, {"type": "text", "key": "options_fitted[additional_options][navigation]", "value": "true"}, {"type": "file", "key": "options_fitted[options_images][]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/options_image1.jpg"}, {"type": "file", "key": "options_fitted[options_images][]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage1.jpg"}, {"key": "options_fitted[has_towbar]", "value": "true", "type": "text", "uuid": "55e88cea-e6c0-485e-b20e-cb9fdc22c107"}, {"key": "options_fitted[has_ventilated_seats]", "value": "false", "type": "text", "uuid": "9455e144-02de-402d-ac65-59a8c9ef9a08"}, {"key": "options_fitted[has_tray_fitted]", "value": "false", "type": "text", "uuid": "356f3a2c-da80-4f4e-a088-2aeff479fc39"}, {"key": "options_fitted[has_canopy_fitted]", "value": "true", "type": "text", "uuid": "bd73195d-70ed-431c-b851-80d4feaacfbd"}, {"key": "options_fitted[has_aftermarket_wheels]", "value": "true", "type": "text", "uuid": "958602ea-2f25-428a-aa52-88d46f09d35a"}, {"key": "options_fitted[has_bull_bar]", "value": "false", "type": "text", "uuid": "aa154938-cef8-41cf-87f8-bdc366785520"}, {"key": "options_fitted[ppsr]", "value": "true", "type": "text", "uuid": "3e13b13c-d7b5-494d-b2f5-8b9087dea19d"}, {"key": "options_fitted[cargo_blind]", "value": "false", "type": "text", "uuid": "796f5302-96bd-43d0-847a-020ab6301d0e"}, {"key": "options_fitted[tonneau_cover]", "value": "true", "type": "text", "uuid": "064b3de5-d9ea-4184-bb20-848f4a3d326e"}, {"key": "options_fitted[tonneau_type]", "value": "hard", "type": "text", "uuid": "e7938dc8-2f61-4f6e-a36f-12381299e6e6"}, {"key": "options_fitted[last_ppsr_date]", "value": "2024-05-05", "type": "text", "uuid": "a18f109f-6e70-4ebe-9925-38f97166e50d"}, {"key": "options_fitted[additional_options][headrest]", "value": "available", "type": "text", "uuid": "79dd0618-1a60-43f1-9025-ef1df61c4c6a"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/ed3410a3-a2e1-4505-9bc4-705b4cb34906/options-fitted", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "ed3410a3-a2e1-4505-9bc4-705b4cb34906", "options-fitted"]}}, "response": []}, {"name": "VehicleHistory", "id": "22710-e432762e-fe25-416e-8138-e24353a4633e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "vehicle_history[number_of_owners]", "value": "2"}, {"type": "text", "key": "vehicle_history[has_accident_history]", "value": "false"}, {"type": "text", "key": "vehicle_history[accident_details]", "value": "major dents"}, {"type": "text", "key": "vehicle_history[next_service_due]", "value": "2025-11-15"}, {"type": "text", "key": "vehicle_history[has_dash_warning_lights]", "value": "true"}, {"type": "text", "key": "vehicle_history[dash_warning_details]", "value": "many errors"}, {"type": "text", "key": "vehicle_history[notes]", "value": "repair work done"}, {"type": "text", "key": "vehicle_history[last_service_date]", "value": "2024-01-15"}, {"type": "text", "key": "vehicle_history[last_service_odometer]", "value": "45000"}, {"type": "text", "key": "vehicle_history[vehicle_history_status]", "value": "full_oem_history"}, {"type": "file", "key": "vehicle_history[service_book_images][]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/service_book_page1.jpg"}, {"type": "file", "key": "vehicle_history[service_book_images][]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/service_book_page2.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/ed3410a3-a2e1-4505-9bc4-705b4cb34906/vehicle-history", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "ed3410a3-a2e1-4505-9bc4-705b4cb34906", "vehicle-history"]}}, "response": []}, {"name": "Dashboard", "id": "22710-11f75359-21b4-448c-817c-95fde6fceeae", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/dashboard", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "dashboard"]}}, "response": []}, {"name": "VehicleFinanceDetails", "id": "22710-74787981-7f68-4794-b34c-ee80c8ce8b44", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"finance\": {\n      \"is_financed\": \"financed\",\n      \"finance_company\": \"ABC Finance\",\n      \"current_repayment_amount\": 100.0,     \n      \"terms_months\": 60,\n      \"interest_rate\": 5.5,\n      \"next_due_date\": \"2025-12-31\",\n      \"has_clear_title\": false,\n      \"payout_amount\": 25000.0\n    }\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/ed3410a3-a2e1-4505-9bc4-705b4cb34906/finance-details", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "ed3410a3-a2e1-4505-9bc4-705b4cb34906", "finance-details"]}}, "response": []}, {"name": "GivenPrice", "id": "22710-e86fa698-9fe1-41ff-a876-38357e0e9d8f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"give_price\": 0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/ed3410a3-a2e1-4505-9bc4-705b4cb34906", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "ed3410a3-a2e1-4505-9bc4-705b4cb34906"]}}, "response": []}, {"name": "UpdateCustomer", "id": "22710-ee03df11-781a-4ef6-9e70-e465196a1871", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_uuid\": \"6d543425-0f54-4a45-a719-022f39dbf981\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/ed3410a3-a2e1-4505-9bc4-705b4cb34906/customer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "ed3410a3-a2e1-4505-9bc4-705b4cb34906", "customer"]}}, "response": []}, {"name": "SendForOffers", "id": "22710-f93f162b-303e-4dcf-9a59-12caee586323", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"offers\": [\n        {\n            \"offer_price\": 26000.00,\n            \"valuer_business_name\": \"XYZ Valuers\",\n            \"valuer_email\": \"<EMAIL>\",\n            \"valuer_first_name\": \"<PERSON>\",\n            \"valuer_last_name\": \"<PERSON>\",\n            \"valuer_mobile_number\": \"+***********\",\n            \"add_valuer\": true\n        },\n        {\n            \"appraisal_valuer_uuid\": 1\n        },\n        {\n            \"is_verbal\": true,\n            \"appraisal_valuer_uuid\": 1,\n            \"offer_price\": 21500.00,\n            \"offer_notes\": \"Valuer verbal assessment\"\n        },\n        {\n            \"is_internal\": true,\n            \"offer_price\": 21000.00\n        },\n        {\n            \"offer_price\": 29000.00,\n            \"offer_notes\": \"New valuer assessment\",\n            \"valuer_business_name\": \"XYZ Valuers\",\n            \"valuer_email\": \"<EMAIL>\",\n            \"valuer_first_name\": \"<PERSON>\",\n            \"valuer_last_name\": \"<PERSON>\",\n            \"valuer_mobile_number\": \"+***********\",\n            \"add_valuer\": false\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/6b35612e-aa03-4004-8604-143042517605/send-for-offers", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "6b35612e-aa03-4004-8604-143042517605", "send-for-offers"]}}, "response": []}, {"name": "EditAppraisalVehicle", "id": "22710-d7393a2a-1d06-4de4-9d7e-8d55a0f91937", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "vehicle[make]", "value": "Toyota", "disabled": true}, {"type": "text", "key": "vehicle[model]", "value": "Cam<PERSON>", "disabled": true}, {"type": "text", "key": "vehicle[vin]", "value": "1HGBH41JXMN109186", "disabled": true}, {"type": "text", "key": "vehicle[rego]", "value": "ABC123", "disabled": true}, {"type": "text", "key": "vehicle[registration_expiry]", "value": "2025-12-31", "disabled": true}, {"type": "text", "key": "vehicle[registration_state]", "value": "NSW", "disabled": true}, {"type": "text", "key": "vehicle[build_year]", "value": "2020", "disabled": true}, {"type": "text", "key": "vehicle[build_month]", "value": "6", "disabled": true}, {"type": "text", "key": "vehicle[compliance_year]", "value": "2020", "disabled": true}, {"type": "text", "key": "vehicle[compliance_month]", "value": "5", "disabled": true}, {"type": "text", "key": "vehicle[exterior_color]", "value": "White", "disabled": true}, {"type": "text", "key": "vehicle[interior_color]", "value": "Black", "disabled": true}, {"type": "text", "key": "vehicle[seat_type]", "value": ""}, {"type": "text", "key": "vehicle[fuel_type]", "value": ""}, {"type": "text", "key": "vehicle[driving_wheels]", "value": ""}, {"type": "text", "key": "vehicle[spare_wheel_type]", "value": ""}, {"type": "text", "key": "vehicle[transmission]", "value": ""}, {"type": "text", "key": "vehicle[body_type]", "value": ""}, {"type": "text", "key": "vehicle[number_of_doors]", "value": ""}, {"type": "text", "key": "vehicle[number_of_seats]", "value": ""}, {"type": "text", "key": "vehicle[engine_kilowatts]", "value": ""}, {"type": "text", "key": "vehicle[engine_number]", "value": "4A-GE", "disabled": true}, {"type": "text", "key": "vehicle[wheel_size_front]", "value": "17", "disabled": true}, {"type": "text", "key": "vehicle[wheel_size_rear]", "value": "17", "disabled": true}, {"type": "text", "key": "vehicle[odometer_reading]", "value": ""}, {"type": "text", "key": "vehicle[odometer_date]", "value": "2024-01-15", "disabled": true}, {"type": "text", "key": "vehicle[redbook_code]", "value": "TOYOTA123", "disabled": true}, {"type": "file", "key": "vehicle[main_photo]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/car_1.png", "disabled": true}, {"type": "file", "key": "vehicle[odometer_reading_photo]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/toyota_logo.png", "disabled": true}, {"key": "vehicle[brand_uuid]", "value": "01dd85cf-8538-40fa-b087-d7d0228c2146", "type": "text", "uuid": "a60ea234-7115-4c26-88b4-cbbed997aa87", "disabled": true}, {"key": "vehicle[media_files][]", "type": "file", "uuid": "ffcac09f-1726-4956-9b8c-03395c5a55c7", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage4.jpg", "disabled": true}, {"key": "vehicle[media_files][]", "type": "file", "uuid": "9747dc82-f802-4868-9085-2c5ca2596263", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage3.jpg", "disabled": true}, {"key": "vehicle[media_files][]", "type": "file", "uuid": "a6b00c74-0980-4b0c-8c36-7039250d19fa", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/damage2.jpg"}, {"key": "vehicle[delete_media_files][]", "value": "79", "type": "text", "uuid": "042056b0-7a20-4063-82ab-b61447a7a4f8"}, {"key": "vehicle[delete_media_files][]", "value": "80", "type": "text", "uuid": "967bd506-8803-4408-885c-620cbc9c002e"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/appraisals/3b77edd8-a952-4cc9-81be-34b43474d699/vehicle", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "appraisals", "3b77edd8-a952-4cc9-81be-34b43474d699", "vehicle"]}}, "response": []}], "id": "22710-ca1ff6e3-7a94-4d63-bdd2-5c46a29fa19c"}, {"name": "User", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "5d49237a-2a7d-42f7-89dc-1634fb828b1d", "exec": ["pm.collectionVariables.set(\"temp-token\", pm.response.json().data[\"temporary_token\"]);", ""], "type": "text/javascript", "packages": {}}}], "id": "22710-11a57cac-6b28-42a4-915f-e22abc1a28e2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text", "disabled": true}, {"key": "App-Version", "value": "{{app_version}}", "type": "text", "disabled": true}, {"key": "App-Build-Number", "value": "{{build}}", "type": "text", "disabled": true}, {"key": "Device-OS", "value": "{{os}}", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"user\":\n    {\n        \"email\": \"<EMAIL>\",\n        \"password\": \"Regional123!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/login", "host": ["{{host}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "2FA Verify", "event": [{"listen": "test", "script": {"id": "7e43ea0e-1926-434a-919a-9dcf6ee2df1c", "exec": ["pm.collectionVariables.set(\"access-token\", pm.response.json().data[\"access_token\"]);", "pm.collectionVariables.set(\"refresh-token\", pm.response.json().data[\"refresh_token\"]);", ""], "type": "text/javascript", "packages": {}}}], "id": "22710-4026c005-2225-4ed2-aa01-6dd3006aad0d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text"}, {"key": "App-Version", "value": "{{app_version}}", "type": "text"}, {"key": "App-Build-Number", "value": "{{build}}", "type": "text"}, {"key": "Device-OS", "value": "{{os}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"otp\": \"785768\",\n    \"method\": \"sms\" // to be only sent when a different method than the preferred one is used\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/verify-2fa", "host": ["{{host}}"], "path": ["api", "v1", "auth", "verify-2fa"]}}, "response": []}, {"name": "resend otp", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "1408589d-b7ed-4416-a4dc-d421db8d1f2d"}}], "id": "22710-4cb94fe3-bddd-4f37-8317-4483ba8a9e07", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text", "disabled": true}, {"key": "App-Version", "value": "{{app_version}}", "type": "text", "disabled": true}, {"key": "App-Build-Number", "value": "{{build}}", "type": "text", "disabled": true}, {"key": "Device-OS", "value": "{{os}}", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"method\": \"email\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/resend-otp", "host": ["{{host}}"], "path": ["api", "v1", "auth", "resend-otp"]}}, "response": []}, {"name": "RefreshToken", "event": [{"listen": "test", "script": {"exec": ["pm.collectionVariables.set(\"access-token\", pm.response.json().data[\"access_token\"]);", "pm.collectionVariables.set(\"refresh-token\", pm.response.json().data[\"refresh_token\"]);", ""], "type": "text/javascript", "packages": {}, "id": "d88c3924-2aec-4dd8-8e06-41829474ba3c"}}], "id": "22710-9e3b0f30-e5e5-4dee-9c18-e9f01e61160f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh-token}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/refresh", "host": ["{{host}}"], "path": ["api", "v1", "auth", "refresh"]}}, "response": []}, {"name": "profile", "id": "22710-78f1c56b-3153-4a9d-b419-09e351f81670", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/profile", "host": ["{{host}}"], "path": ["api", "v1", "profile"]}}, "response": []}, {"name": "devices", "id": "22710-8938ba01-aa63-4ebb-b964-faa391773f87", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/devices", "host": ["{{host}}"], "path": ["api", "v1", "devices"]}}, "response": []}, {"name": "Logout", "id": "22710-1e4b64d1-06b6-4030-b889-badba9c99870", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "url": {"raw": "{{host}}/api/v1/auth/logout", "host": ["{{host}}"], "path": ["api", "v1", "auth", "logout"]}}, "response": []}, {"name": "LogoutAllDevices", "id": "22710-8fd282d4-ff2a-4e5b-9d17-1fd14905a2b0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "url": {"raw": "{{host}}/api/v1/devices", "host": ["{{host}}"], "path": ["api", "v1", "devices"]}}, "response": []}, {"name": "ProfilePhoto", "id": "22710-dc6481d7-eaca-4484-808e-e992999ac556", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "file", "key": "photo", "src": "/Users/<USER>/Temp/CleanShot 2025-06-18 at <EMAIL>"}]}, "url": {"raw": "{{host}}/api/v1/profile/photo", "host": ["{{host}}"], "path": ["api", "v1", "profile", "photo"]}}, "response": []}, {"name": "DeletePhoto", "id": "22710-c9d25e32-c7ac-4592-b67d-214a5af33265", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/profile/photo", "host": ["{{host}}"], "path": ["api", "v1", "profile", "photo"]}}, "response": []}, {"name": "me", "id": "22710-b4a82c62-8173-4df6-8f7c-3193de821dcd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/users/me", "host": ["{{host}}"], "path": ["api", "v1", "users", "me"]}}, "response": []}, {"name": "ChangePassword", "id": "22710-2948e4a3-2ed0-4d08-9da3-3ab8b360f70e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"Regional1234!\",\n    \"new_password\": \"Regional123!\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/change-password", "host": ["{{host}}"], "path": ["api", "v1", "auth", "change-password"]}}, "response": []}, {"name": "ForgotPassword", "event": [{"listen": "test", "script": {"id": "a73fde7a-49cf-4cf2-858b-c15ac21f133c", "exec": ["pm.collectionVariables.set(\"temp-token\", pm.response.json().data[\"temporary_token\"]);", ""], "type": "text/javascript", "packages": {}}}], "id": "22710-92dc14fd-8f15-4d86-b900-a4fe13030dd0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/forgot-password", "host": ["{{host}}"], "path": ["api", "v1", "auth", "forgot-password"]}}, "response": []}, {"name": "verify-reset-code", "event": [{"listen": "test", "script": {"id": "7b42c0d1-1e46-413e-9bf0-9aa8913ea132", "exec": ["pm.collectionVariables.set(\"temp-token\", pm.response.json().data[\"temporary_token\"]);"], "type": "text/javascript", "packages": {}}}], "id": "22710-5fe178f4-0124-42f4-b387-c40af57d6d80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"reset_code\": \"913381\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/verify-reset-code", "host": ["{{host}}"], "path": ["api", "v1", "auth", "verify-reset-code"]}}, "response": []}, {"name": "ResetPassword", "id": "22710-d3cb1ffd-7746-4502-a406-89775689a759", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "<PERSON><PERSON><PERSON><PERSON>", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"new_password\": \"Regional123!\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/reset-password", "host": ["{{host}}"], "path": ["api", "v1", "auth", "reset-password"]}}, "response": []}, {"name": "devices update", "id": "22710-2c53c552-7d74-426f-8e07-a4f64f07eb7f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"device_os\": \"ios\",\n    \"device_os_version\": \"17.2.1\",\n    \"app_version\": \"2.0.0\",\n    \"app_build_number\": \"200\",\n    \"fcm_token\": \"fcm-token-123\",\n    \"device_name\": \"iPhone 15 Pro\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/devices", "host": ["{{host}}"], "path": ["api", "v1", "devices"]}}, "response": []}, {"name": "profile update", "id": "22710-48027325-0944-4354-802e-e64e67b33d3e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"preferred_2fa\": \"sms\",\n    \"job_title\": \"Senior Technician\",\n    \"preferred_language\": \"english\",\n    \"time_zone\": \"melbourne\",\n    \"onboarding_completed\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/profile", "host": ["{{host}}"], "path": ["api", "v1", "profile"]}}, "response": []}, {"name": "setup 2FA", "id": "22710-196fd6d9-baf2-40d0-8fed-0b61474ccc94", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/auth/setup-2fa", "host": ["{{host}}"], "path": ["api", "v1", "auth", "setup-2fa"]}}, "response": []}, {"name": "verify 2FA Setup", "id": "22710-24c0678c-892c-4c0a-855a-dd12d31c60f3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"032914\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/verify-2fa-setup", "host": ["{{host}}"], "path": ["api", "v1", "auth", "verify-2fa-setup"]}}, "response": []}, {"name": "change 2FA method", "id": "22710-d3988c71-0796-401b-a99a-f7ea7f3d0615", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"method\": \"email\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/request-2fa-method", "host": ["{{host}}"], "path": ["api", "v1", "auth", "request-2fa-method"]}}, "response": []}, {"name": "GetDriverLicense", "id": "22710-bb8218d8-2c29-401a-a4e0-91ca6b2f2cb3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "SaveDL", "id": "22710-d235f724-bdd0-4406-8998-c0e06f534626", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"licence_number\": \"**********9\",\n    \"expiry_date\": \"2025-12-31\",\n    \"issue_date\": \"2020-01-01\",\n    \"category\": \"C\",\n    \"issuing_country\": \"au\",\n    \"issuing_state\": \"NSW\",\n    \"full_name\": \"<PERSON>\",\n    \"date_of_birth\": \"1991-01-01\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "SaveDLWithImages", "id": "22710-27c01db9-d78c-481c-8bc3-840b9b7e0709", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "licence_number", "value": "**********9", "type": "text", "uuid": "ce3f3a50-e285-48a4-891c-1597dc560f10"}, {"key": "expiry_date", "value": "2025-12-31", "type": "text", "uuid": "f04a572b-3090-40e6-b315-ae24ecc0313f"}, {"key": "issue_date", "value": "2020-01-01", "type": "text", "uuid": "184ca0f6-d45e-4205-b51a-9f8598968b97"}, {"key": "category", "value": "C", "type": "text", "uuid": "1914bda7-b573-43c6-9fed-d57b4636324c"}, {"key": "issuing_country", "value": "au", "type": "text", "uuid": "6b920974-2ff5-48a1-af4a-3a66261bf2ca"}, {"key": "issuing_state", "value": "NSW", "type": "text", "uuid": "f3df5167-ef0c-45fd-9e17-47b32f24c460"}, {"key": "full_name", "value": "<PERSON>", "type": "text", "uuid": "2ac40448-8d2a-4408-b34f-fe5a68ef5759"}, {"key": "date_of_birth", "value": "1991-01-01", "type": "text", "uuid": "3a4c3320-08b9-41ff-ab44-0e56b98e172f"}, {"key": "front_image", "type": "file", "uuid": "39f1edbf-5988-4e8b-bb50-6654a96884ca", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/front_dl.png"}, {"key": "back_image", "type": "file", "uuid": "0f8a7108-1b24-4a03-a6be-557b859d3e76", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/back_dl.png"}]}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "DeleteDL", "id": "22710-ba712242-eb4e-4487-bbdd-d2a6963d7d36", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "DLImage", "id": "22710-c3e124e9-ab4c-4d8b-b660-3d204c85c60c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "image_type", "value": "front", "type": "text", "uuid": "e76ae103-a162-40c2-b609-11f9ccbe8aa0"}, {"key": "image", "type": "file", "uuid": "b170e737-74ef-471c-b0e7-69d093430256", "src": "/Users/<USER>/Temp/CleanShot 2025-06-16 at <EMAIL>"}]}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license-image", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license-image"]}}, "response": []}, {"name": "DeleteDLImage", "id": "22710-0f5cfa31-9ce8-470e-9d4b-0d20ec80b3cb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"image_type\": \"back\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license-image", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license-image"]}}, "response": []}, {"name": "user_invitations", "id": "22710-9ed84028-5af2-49d0-95ec-7ec9d49edfba", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"user\": {\n        \"email\": \"<EMAIL>\",\n        \"first_name\": \"Test\",\n        \"last_name\": \"User\",\n        \"phone\": \"+************\",\n        \"user_type\": \"dealership_user\",\n        \"dealership_uuid\": \"9d7267a7-108b-4e62-9d6d-77cd3d86d787\",\n        \"role_type\": \"sales_person\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}api/v1/user_invitations", "host": ["{{host}}api"], "path": ["v1", "user_invitations"]}}, "response": []}], "id": "22710-299979de-2f0c-4d8c-8372-b46a6d567e85"}, {"name": "Drives", "item": [{"name": "CreateBooking", "id": "22710-fd6ad63e-c452-4b06-b5a0-9524c216fcf1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"vehicle_uuid\": \"9c1f3567-dbce-4b1a-a9c2-b85a651750b6\",\n    \"drive_type\": \"test_drive_booking\",\n    \"expected_pickup_date_time\": \"2025-06-26T00:30:53.169Z\",\n    \"expected_return_date_time\": \"2025-06-27T00:30:53.169Z\",\n    // \"sales_person_uuid\": \"34c368a7-1f77-4492-8eb6-531265c6c16f\",\n    // \"customer_uuid\": \"string\",\n    \"notes\": \"string\",\n    \"customer_info\": {\n        \"first_name\": \"string\",\n        \"last_name\": \"string\",\n        \"age\": 25,\n        \"email\": \"<EMAIL>\",\n        \"phone_number\": \"string\",\n        \"gender\": \"male\",\n        \"address_line1\": \"string\",\n        \"address_line2\": \"string\",\n        \"suburb\": \"string\",\n        \"city\": \"string\",\n        \"state\": \"string\",\n        \"country\": \"string\",\n        \"postcode\": \"string\",\n        \"company_name\": \"string\",\n        \"driver_license\": {\n            \"licence_number\": \"string\",\n            \"expiry_date\": \"2025-12-31\",\n            \"issuing_state\": \"string\",\n            \"issuing_country\": \"string\",\n            \"full_name\": \"string\",\n            \"date_of_birth\": \"1990-01-01\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings"]}}, "response": []}, {"name": "Get Bookings", "id": "22710-5db5d53c-984d-4fcb-b81c-682a150ab042", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings?drive_type=test_drive_booking&status=scheduled&start_date=2025-06-01&end_date=2025-08-08&per_page=20&page=1&vehicle_uuid=9c1f3567-dbce-4b1a-a9c2-b85a651750b6&sales_person_uuid=b020f4ac-d938-44ac-982c-21343fdc5fec", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings"], "query": [{"key": "drive_type", "value": "test_drive_booking"}, {"key": "status", "value": "scheduled"}, {"key": "start_date", "value": "2025-06-01"}, {"key": "end_date", "value": "2025-08-08"}, {"key": "per_page", "value": "20"}, {"key": "page", "value": "1"}, {"key": "vehicle_uuid", "value": "9c1f3567-dbce-4b1a-a9c2-b85a651750b6"}, {"key": "sales_person_uuid", "value": "b020f4ac-d938-44ac-982c-21343fdc5fec"}]}}, "response": []}, {"name": "Get Booking", "id": "22710-07f74823-e2e7-4c9d-8fa9-0afa4c7b01bc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/752b07db-21d7-47d5-bd49-71ce068468a8", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "752b07db-21d7-47d5-bd49-71ce068468a8"]}}, "response": []}, {"name": "Cancel Booking", "id": "22710-c45f9c44-c313-4adf-8fea-cc0891a37513", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"cancel_reason\": \"Cancel\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/752b07db-21d7-47d5-bd49-71ce068468a8/cancel", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "752b07db-21d7-47d5-bd49-71ce068468a8", "cancel"]}}, "response": []}, {"name": "Update Booking", "id": "22710-d87c802f-96c6-4582-af72-1ca505ad127c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"vehicle_uuid\": \"9c1f3567-dbce-4b1a-a9c2-b85a651750b6\",\n    \"expected_pickup_date_time\": \"2025-06-26T00:30:53.169Z\",\n    \"expected_return_date_time\": \"2025-06-27T00:30:53.169Z\",\n    \"sales_person_uuid\": \"1d3dc6cc-05e5-4ae7-b105-6d7febeeb632\",\n    \"notes\": \"New Notes\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/752b07db-21d7-47d5-bd49-71ce068468a8", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "752b07db-21d7-47d5-bd49-71ce068468a8"]}}, "response": []}, {"name": "CreateBookingWithDL", "id": "22710-a1dea058-3886-46be-841c-276d942cfc69", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "booking[vehicle_uuid]", "value": "bfad23a6-**************-5830bda3ff91"}, {"type": "text", "key": "booking[drive_type]", "value": "test_drive_booking"}, {"type": "text", "key": "booking[expected_pickup_date_time]", "value": "2025-06-26T00:30:53.169Z"}, {"type": "text", "key": "booking[expected_return_date_time]", "value": "2025-06-27T00:30:53.169Z"}, {"type": "text", "key": "booking[notes]", "value": "string"}, {"type": "text", "key": "booking[customer_info][first_name]", "value": "<PERSON>"}, {"type": "text", "key": "booking[customer_info][last_name]", "value": "<PERSON><PERSON>"}, {"type": "text", "key": "booking[customer_info][age]", "value": "25"}, {"type": "text", "key": "booking[customer_info][email]", "value": "<EMAIL>"}, {"type": "text", "key": "booking[customer_info][phone_number]", "value": "0987654321"}, {"type": "text", "key": "booking[customer_info][gender]", "value": "male"}, {"type": "text", "key": "booking[customer_info][address_line1]", "value": "Main Street 1"}, {"type": "text", "key": "booking[customer_info][address_line2]", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "text", "key": "booking[customer_info][suburb]", "value": "Koelpin Trace"}, {"type": "text", "key": "booking[customer_info][city]", "value": "NewEast Estebanberg"}, {"type": "text", "key": "booking[customer_info][state]", "value": "New South Wales"}, {"type": "text", "key": "booking[customer_info][country]", "value": "Australia"}, {"type": "text", "key": "booking[customer_info][postcode]", "value": "4215"}, {"type": "text", "key": "booking[customer_info][company_name]", "value": "Buckridge Inc"}, {"type": "text", "key": "booking[customer_info][driver_license][licence_number]", "value": "*********"}, {"type": "text", "key": "booking[customer_info][driver_license][expiry_date]", "value": "2025-12-31"}, {"type": "text", "key": "booking[customer_info][driver_license][issuing_state]", "value": "New South Wales"}, {"type": "text", "key": "booking[customer_info][driver_license][issuing_country]", "value": "Australia"}, {"type": "text", "key": "booking[customer_info][driver_license][full_name]", "value": "<PERSON>"}, {"type": "text", "key": "booking[customer_info][driver_license][date_of_birth]", "value": "1990-01-01"}, {"type": "file", "key": "booking[customer_info][driver_license][front_image]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/images.jpeg"}, {"type": "file", "key": "booking[customer_info][driver_license][back_image]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/93995951_2626626737551587_3656803870996168704_n.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings"]}}, "response": []}, {"name": "GetDrive", "id": "22710-788bab34-28b2-42e3-8757-5b279102b2ff", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/a18e1703-e2f2-4818-9499-9959060dacdc", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "a18e1703-e2f2-4818-9499-9959060dacdc"]}}, "response": []}, {"name": "Reassign <PERSON>erson", "id": "22710-d3e07a2d-2a1b-4002-ac7a-bf4662793233", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"sales_person_uuid\": \"8861bcb5-f708-4ac1-a7fe-afda8d02eddb\",\n    \"sales_person_accompanying_uuid\": \"8cc81022-93f7-40a6-9bda-106e4dfec79d\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/1f57f644-12b5-4c30-9ceb-9a7ce8ad387f/reassign", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "1f57f644-12b5-4c30-9ceb-9a7ce8ad387f", "reassign"]}}, "response": []}, {"name": "CreateDrive", "id": "22710-7cb0a431-f8ee-4629-92df-bad3d98453f3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"vehicle_uuid\": \"38de96f4-ad53-4251-addf-8e74b6cece3f\",\n    \"drive_type\": \"test_drive\",\n    \"sales_person_uuid\": \"8861bcb5-f708-4ac1-a7fe-afda8d02eddb\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives"]}}, "response": []}, {"name": "Activities", "id": "22710-7e327bdd-ebde-40e5-b68f-6aa2fdccfc80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives?start_date_from=2025-06-01&start_date_to=2025-07-07&vehicle_uuid=bfad23a6-**************-5830bda3ff91&sales_person_uuid=8861bcb5-f708-4ac1-a7fe-afda8d02eddb&sold_status=sold&overdue=true&drive_type=enquiry&customer_uuid=ffde228a-f91e-4a47-ab3d-022baa286444&status=completed&trade_plate_uuid=8861bcb5-f708-4ac1-a7fe-afda8d02eddb&updated_from=2025-06-01&updated_to=2025-07-07", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives"], "query": [{"key": "start_date_from", "value": "2025-06-01"}, {"key": "start_date_to", "value": "2025-07-07"}, {"key": "vehicle_uuid", "value": "bfad23a6-**************-5830bda3ff91"}, {"key": "sales_person_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb"}, {"key": "sold_status", "value": "sold"}, {"key": "overdue", "value": "true", "type": "text"}, {"key": "drive_type", "value": "enquiry"}, {"key": "customer_uuid", "value": "ffde228a-f91e-4a47-ab3d-022baa286444"}, {"key": "status", "value": "completed"}, {"key": "trade_plate_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb"}, {"key": "updated_from", "value": "2025-06-01", "type": "text"}, {"key": "updated_to", "value": "2025-07-07", "type": "text"}]}}, "response": []}, {"name": "CreateDamageReport", "id": "22710-dd5010ff-4895-41d3-8511-2d281374882f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "damage_type", "value": "initial"}, {"type": "text", "key": "damage_notes", "value": "Minor scratches on the front bumper"}, {"type": "file", "key": "media_files[]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/large_video.mp4"}, {"type": "file", "key": "media_files[]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/car_1.png"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/4a2bc06a-02b4-47ff-9b4d-5b4457fd247e/damage-report", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "4a2bc06a-02b4-47ff-9b4d-5b4457fd247e", "damage-report"]}}, "response": []}, {"name": "Assign Tradeplate", "id": "22710-c51f8537-6d5a-4987-a506-e8c43cd7bf1c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"trade_plate_uuid\": \"1e923c6f-a497-43fb-b437-4654afd61bec\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/7d5caf38-a2ea-488e-bce2-ae81c907af24/trade-plate", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "7d5caf38-a2ea-488e-bce2-ae81c907af24", "trade-plate"]}}, "response": []}, {"name": "UpdateDriveCustomerExisting", "id": "22710-e23cc0dc-5757-493a-83a3-2b7411fcc263", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"customer_uuid\": \"80a2e7b5-2eea-4160-9a03-28ecfb4afc19\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/9eb414ca-b44b-4617-baeb-c93853bd989d/customer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "9eb414ca-b44b-4617-baeb-c93853bd989d", "customer"]}}, "response": []}, {"name": "UpdateDriveCustomerNew", "id": "22710-9a817ab6-4605-4248-a3a6-d6b1df4276c4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "customer_info[first_name]", "value": "<PERSON>"}, {"type": "text", "key": "customer_info[last_name]", "value": "<PERSON><PERSON>"}, {"type": "text", "key": "customer_info[email]", "value": "<EMAIL>"}, {"type": "text", "key": "customer_info[phone_number]", "value": "+61412345678"}, {"type": "text", "key": "customer_info[driver_license][licence_number]", "value": "**********"}, {"type": "text", "key": "customer_info[driver_license][expiry_date]", "value": "2025-12-31"}, {"type": "file", "key": "customer_info[driver_license][front_image]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/back_dl.png"}, {"type": "file", "key": "customer_info[driver_license][back_image]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/front_dl.png"}, {"key": "customer_info[age]", "value": "34", "type": "text", "uuid": "9207164c-45b7-4ffd-8123-01023ad87bf6"}, {"key": "customer_info[gender]", "value": "male", "type": "text", "uuid": "5e6ec691-79a7-42d1-a80f-c82beb8f6a49"}, {"key": "customer_info[address_line1]", "value": "Address1", "type": "text", "uuid": "0388e973-1c7f-487e-b1fe-c0e92e8444f6"}, {"key": "customer_info[address_line2]", "value": "Address1", "type": "text", "uuid": "f20fde14-5135-489c-871e-e31accf7279a"}, {"key": "customer_info[suburb]", "value": "Suburb", "type": "text", "uuid": "24d75146-98fa-4447-a04c-f56d7bac1bcb"}, {"key": "customer_info[city]", "value": "Sydney", "type": "text", "uuid": "d2c424e9-e8c3-4b15-8eb7-5cb63640ad43"}, {"key": "customer_info[state]", "value": "NSW", "type": "text", "uuid": "000260a0-c8ca-4fa5-b2be-9e72d7f62c4d"}, {"key": "customer_info[country]", "value": "Australia", "type": "text", "uuid": "32d7156e-4fdb-4ee6-94cc-dc0ce64ae41b"}, {"key": "customer_info[postcode]", "value": "4215", "type": "text", "uuid": "92697aca-def7-45b2-baab-8fd19b50c91a"}, {"key": "customer_info[company_name]", "value": "Buckridge Inc", "type": "text", "uuid": "762d1a0c-987f-4bed-b38f-e3759f458d84"}, {"key": "customer_info[driver_license][issuing_state]", "value": "NSW", "type": "text", "uuid": "58a1b607-9605-45b7-b79c-9b87a06f601a"}, {"key": "customer_info[driver_license][issuing_country]", "value": "Australia", "type": "text", "uuid": "adf2d759-43b8-4701-a0e5-ef6b79895c4e"}, {"key": "customer_info[driver_license][full_name]", "value": "Tester <PERSON>", "type": "text", "uuid": "2ec35119-43b8-4e48-9680-2a6205ec4960"}, {"key": "customer_info[driver_license][date_of_birth]", "value": "1983-02-11", "type": "text", "uuid": "513f54ef-9ef0-4e2e-b414-763bd5459e15"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/9eb414ca-b44b-4617-baeb-c93853bd989d/customer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "9eb414ca-b44b-4617-baeb-c93853bd989d", "customer"]}}, "response": []}, {"name": "createVehicleWithmedia_files", "id": "22710-4e7a9997-472e-4233-a528-768a4a4bed2f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "vehicle[vin]", "value": "1HGBH41JXMN109186"}, {"type": "text", "key": "vehicle[stock_number]", "value": "TOY001"}, {"type": "text", "key": "vehicle[rego]", "value": "ABC123"}, {"type": "text", "key": "vehicle[make]", "value": "Toyota"}, {"type": "text", "key": "vehicle[model]", "value": "Cam<PERSON>"}, {"type": "text", "key": "vehicle[build_year]", "value": "2024"}, {"type": "text", "key": "vehicle[color]", "value": "Blue"}, {"type": "text", "key": "vehicle[vehicle_type]", "value": "new_vehicle"}, {"type": "text", "key": "vehicle[status]", "value": "available"}, {"type": "text", "key": "vehicle[last_known_odometer_km]", "value": "10000"}, {"type": "text", "key": "vehicle[last_known_fuel_gauge_level]", "value": "50"}, {"type": "text", "key": "vehicle[available_for_drive]", "value": "true"}, {"type": "text", "key": "vehicle[is_trade_plate_used]", "value": "false"}, {"type": "text", "key": "vehicle[rego_expiry]", "value": "2025-12-31"}, {"type": "file", "key": "vehicle[media_files][]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/images.jpeg"}, {"type": "file", "key": "vehicle[media_files][]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/independence.png"}, {"key": "vehicle[brand_uuid]", "value": "7266a3d0-e278-41a7-934e-29b79832c3d4", "type": "text", "uuid": "3ec1032b-853d-4f6c-ae7d-8e5057a157f7"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/vehicles", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "vehicles"]}}, "response": []}, {"name": "createVehicleWOPhoto", "id": "22710-28b8ee37-2ea2-42eb-9bf0-bd57d8fde21a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"make\": \"Toyota\",\n    \"model\": \"Camry\",\n    \"build_year\": 2023,\n    \"color\": \"Blue\",\n    \"vehicle_type\": \"new_vehicle\",\n    \"vin\": \"1HGBH41JXMN109186\",\n    \"stock_number\": \"TOY001\",\n    \"rego\": \"ABC123\",\n    \"status\": \"available\",\n    \"last_known_odometer_km\": 10000,\n    \"last_known_fuel_gauge_level\": 50,\n    \"available_for_drive\": true,\n    \"is_trade_plate_used\": false,\n    \"rego_expiry\": \"2026-12-31\",\n    \"brand_uuid\": \"7266a3d0-e278-41a7-934e-29b79832c3d4\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/vehicles", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "vehicles"]}}, "response": []}, {"name": "CreateEnquiry", "id": "22710-ed20f869-3886-4591-b82d-1e3370f6225d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n   \"enquiry\": {\n      \"vehicle_uuid\": \"6bb48864-77d3-45c5-9d55-f82b2da19629\",\n      \"customer_uuid\": \"6a9f0ba3-41a6-44db-a9b2-0dd3597d7436\",\n    //   \"sales_person_uuid\": \"b5e7cf46-02e0-4737-824c-8fab66688acb\",\n      \"notes\": \"Customer enquiry about vehicle features\"\n   }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/enquiries", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "enquiries"]}}, "response": []}, {"name": "Drives Dashboard", "id": "22710-c42a5e2d-fd04-46fd-a577-6335779173d8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/dashboard", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "dashboard"]}}, "response": []}, {"name": "Complete Drive", "id": "22710-125cbb01-a34c-489c-b0d2-cf6c34c7548d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"enjoyed2\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/048f1841-ea1b-4867-a779-d7c30d039e87/complete", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "048f1841-ea1b-4867-a779-d7c30d039e87", "complete"]}}, "response": []}, {"name": "Start Drive", "id": "22710-26acf6a9-3e9b-4e47-857c-3b1706855cd9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"sales_person_accompanying\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/5bf7a1a6-6763-44e5-b4c8-2f826d03bbdb/start", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "5bf7a1a6-6763-44e5-b4c8-2f826d03bbdb", "start"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "id": "22710-1a56bce4-52e7-4129-bd48-9ebeb11345dd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"sold_status\": \"sold\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/833c0489-db34-4ecc-aa11-fa39ba032c70/sold-status", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "833c0489-db34-4ecc-aa11-fa39ba032c70", "sold-status"]}}, "response": []}, {"name": "GetDrives", "id": "22710-3ddeefb1-ce37-45df-acd5-df5b99ad73e5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives?sold_status=sold&query=Rav4", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives"], "query": [{"key": "vehicle_uuid", "value": "617c748e-299d-4f25-bb90-6935ccaa512a", "disabled": true}, {"key": "customer_uuid", "value": "ffde228a-f91e-4a47-ab3d-022baa286444", "disabled": true}, {"key": "drive_type", "value": "test_drive", "disabled": true}, {"key": "status", "value": "draft", "disabled": true}, {"key": "sales_person_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb", "disabled": true}, {"key": "sold_status", "value": "sold"}, {"key": "trade_plate_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb", "disabled": true}, {"key": "start_date_from", "value": "2025-05-01", "disabled": true}, {"key": "start_date_to", "value": "2025-08-01", "disabled": true}, {"key": "eligible_for_return", "value": "true", "type": "text", "disabled": true}, {"key": "overdue", "value": "true", "type": "text", "disabled": true}, {"key": "updated_from", "value": "2025-05-01", "disabled": true}, {"key": "updated_to", "value": "2025-08-01", "disabled": true}, {"key": "query", "value": "Rav4"}]}}, "response": []}, {"name": "attachCustomerSign", "id": "22710-353ed9a0-5499-4a62-94eb-ecd3b5a2f077", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"key": "signature", "type": "file", "uuid": "0b543378-772d-4d7c-8b60-1abeec8982ae", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/profile.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/e7c794ef-2767-4591-b8c3-47ba57cccad4/customer-signature", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "e7c794ef-2767-4591-b8c3-47ba57cccad4", "customer-signature"]}}, "response": []}, {"name": "updateLocation", "id": "22710-324ed7c3-4be1-4d39-861e-f99a98a3273a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": -37.8136,\n    \"longitude\": 144.9631,\n    \"accuracy\": 5.0\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/a5a55fe8-1ab3-43b7-a48e-583738e23b25/location", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "a5a55fe8-1ab3-43b7-a48e-583738e23b25", "location"]}}, "response": []}, {"name": "UpdateTime", "id": "22710-880e10f9-c711-4eeb-87c5-7422942947bc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"expected_return_datetime\": \"2025-07-10T15:30:00Z\",\n    \"expected_pickup_datetime\": \"2025-07-12T15:30:00Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/a18e1703-e2f2-4818-9499-9959060dacdc/time", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "a18e1703-e2f2-4818-9499-9959060dacdc", "time"]}}, "response": []}, {"name": "createDriveFromBooking", "id": "22710-b0851b48-4e2a-46c1-8ce1-a6f07f99176a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/e08a383b-a33d-4970-b952-2d505521936d/create-drive", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "e08a383b-a33d-4970-b952-2d505521936d", "create-drive"]}}, "response": []}, {"name": "Start Odometer", "id": "22710-c9fe2197-637f-4922-b5e2-d8f3c0e592fa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"start_odometer_reading\": 500,\n    \"start_fuel_gauge_level\": 70\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/df248898-ead4-4e75-a384-b9ecfa71f59f/odometer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "df248898-ead4-4e75-a384-b9ecfa71f59f", "odometer"]}}, "response": []}, {"name": "End Odometer", "id": "22710-ee2994d8-5745-4e55-875d-fd7a95a33f7e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"end_odometer_reading\": 550,\n    \"end_fuel_gauge_level\": 60\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/62cb1041-21fa-4c59-b7c2-65cc2f18d9e1/odometer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "62cb1041-21fa-4c59-b7c2-65cc2f18d9e1", "odometer"]}}, "response": []}, {"name": "DeleteDrive", "id": "22710-8493b69d-d81e-464f-bab3-acf733c5a110", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/a4384c29-0d9f-408f-93f1-92b51c8bb49d", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "a4384c29-0d9f-408f-93f1-92b51c8bb49d"]}}, "response": []}], "id": "22710-6773caea-d418-46cb-b076-fb96e7978598"}, {"name": "Dealerships", "item": [{"name": "Customers", "id": "22710-b8d7f2f0-7657-4fdd-a4ba-7bb247a4762e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers?per_page=1000", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers"], "query": [{"key": "per_page", "value": "1000"}]}}, "response": []}, {"name": "Customers Autocomplete Search", "id": "22710-367951ef-f509-4032-bcc8-d8bcb44a6c73", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers/search?query=John", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers", "search"], "query": [{"key": "query", "value": "<PERSON>"}]}}, "response": []}, {"name": "CreateCustomer", "id": "22710-8c30261f-2633-4689-a9a3-2ad39fa364e4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\" ,\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+61400000000\",\n  \"age\": 30,\n  \"gender\": \"male\",\n  \"company_name\": \"Acme Corp\",\n  \"postcode\": \"2000\",\n  \"suburb\": \"Sydney\",\n  \"address_line1\": \"123 Main St\",\n  \"city\": \"Sydney\",\n  \"state\": \"NSW\",\n  \"country\": \"au\" \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers"]}}, "response": []}, {"name": "ShowCustomer", "id": "22710-c38a47b3-06b5-4ca3-8380-69449739b7e2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers/ce852131-d092-4717-a36c-6661b4dfcd85", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers", "ce852131-d092-4717-a36c-6661b4dfcd85"]}}, "response": []}, {"name": "UpdateCustomer", "id": "22710-753559c7-a67f-431a-b430-81456c13172a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON><PERSON>\",\n    \"last_name\": \"<PERSON><PERSON><PERSON>\" ,\n    \"email\": \"<EMAIL>\",\n    \"phone_number\": \"+61400000001\",\n    \"age\": 31,\n    \"gender\": \"male\",\n    \"company_name\": \"Acme Corp.\",\n    \"postcode\": \"2001\",\n    \"suburb\": \"Sydney\",\n    \"address_line1\": \"124 Main St\",\n    \"city\": \"Sydney\",\n    \"state\": \"NSW\",\n    \"country\": \"au\" \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers/ce852131-d092-4717-a36c-6661b4dfcd85", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers", "ce852131-d092-4717-a36c-6661b4dfcd85"]}}, "response": []}, {"name": "usersByDealership", "id": "22710-2ea69ab8-cc17-49c5-967b-1802d08d4a9a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/users?role_type=sales_person&page=2&per_page=2", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "users"], "query": [{"key": "role_type", "value": "sales_person"}, {"key": "page", "value": "2"}, {"key": "per_page", "value": "2"}]}}, "response": []}, {"name": "dealerships", "id": "22710-fdfbef46-61d8-4b11-a8d9-d2e4833204ac", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships", "host": ["{{host}}"], "path": ["api", "v1", "dealerships"]}}, "response": []}, {"name": "brands", "id": "22710-d1d177ec-6196-4dff-bb55-6d69249b4e22", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/brands", "host": ["{{host}}"], "path": ["api", "v1", "brands"]}}, "response": []}, {"name": "getAgreements", "id": "22710-0b7d36c8-61cf-4e0e-80b8-78d4225589d0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/agreements", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "agreements"]}}, "response": []}, {"name": "trade-plates", "id": "22710-cfd4dbab-c950-44ee-86f3-99a2f495b346", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/trade-plates", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "trade-plates"]}}, "response": []}, {"name": "vehicles", "id": "22710-88c9eac8-57a2-4e45-b0ff-2f7495e530e4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/vehicles?per_page=50&page=1&vehicle_type=new_vehicle&query=camry", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "vehicles"], "query": [{"key": "per_page", "value": "50"}, {"key": "page", "value": "1"}, {"key": "vehicle_type", "value": "new_vehicle"}, {"key": "query", "value": "camry"}]}}, "response": []}], "id": "22710-4f60e9ef-4ebd-4080-9eb3-dce1a99e05bb"}], "event": [{"listen": "prerequest", "script": {"id": "74f00bd6-83e8-4266-995d-57483d3835c2", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "8d0d261a-c462-45f5-9734-917764d3873e", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "device_id", "value": "DeviceID", "type": "default"}, {"key": "app_version", "value": "11", "type": "default"}, {"key": "build", "value": "1212", "type": "default"}, {"key": "os", "value": "ios", "type": "default"}, {"key": "access-token", "value": ""}, {"key": "refresh-token", "value": ""}, {"key": "temp-token", "value": ""}, {"key": "host", "value": "https://morning-escarpment-47088-1580637df638.herokuapp.com", "type": "string"}]}